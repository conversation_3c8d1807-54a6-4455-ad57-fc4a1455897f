#!/usr/bin/env python3
"""
Script pour migrer la base de données et ajouter la colonne question
"""

import sqlite3
import os

def migrate_database():
    """Migre la base de données pour ajouter la colonne question"""
    db_path = 'instance/database.db'
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne question existe déjà
        cursor.execute("PRAGMA table_info(tarot_reading)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'question' in columns:
            print("✅ La colonne 'question' existe déjà")
            conn.close()
            return True
        
        print("🔧 Ajout de la colonne 'question' à la table tarot_reading...")
        
        # Ajouter la colonne question
        cursor.execute("ALTER TABLE tarot_reading ADD COLUMN question VARCHAR(500)")
        
        # Valider les changements
        conn.commit()
        
        print("✅ Migration réussie!")
        
        # Vérifier que la colonne a été ajoutée
        cursor.execute("PRAGMA table_info(tarot_reading)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📊 Colonnes dans tarot_reading: {columns}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        return False

if __name__ == "__main__":
    print("🔮 Migration de la base de données...")
    print("-" * 40)
    
    if migrate_database():
        print("🎉 Migration terminée avec succès!")
    else:
        print("💥 Échec de la migration")

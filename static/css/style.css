/* L'Arcanum Éveillé - Dark Theme Styles */

:root {
    --dark-bg: #0a0a0a;
    --dark-secondary: #1a1a1a;
    --dark-tertiary: #2a2a2a;
    --gold: #d4af37;
    --gold-light: #f4d03f;
    --gold-dark: #b7950b;
    --purple-deep: #4a148c;
    --purple-light: #7b1fa2;
    --blue-mystical: #1a237e;
    --red-blood: #b71c1c;
    --silver: #c0c0c0;
    --text-light: #e0e0e0;
    --text-muted: #9e9e9e;
}

/* Global Styles */
body.dark-theme {
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 100%);
    color: var(--text-light);
    font-family: 'Crimson Text', serif;
    min-height: 100vh;
}

.mystical-font {
    font-family: 'Cinzel', serif;
    font-weight: 600;
}

/* Colors */
.text-gold { color: var(--gold) !important; }
.text-silver { color: var(--silver) !important; }
.text-purple { color: var(--purple-light) !important; }
.bg-gold { background-color: var(--gold) !important; }
.bg-dark-custom { background-color: var(--dark-secondary) !important; }
.bg-dark-tertiary { background-color: var(--dark-tertiary) !important; }

/* Buttons */
.btn-outline-gold {
    border-color: var(--gold);
    color: var(--gold);
    transition: all 0.3s ease;
}

.btn-outline-gold:hover {
    background-color: var(--gold);
    color: var(--dark-bg);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.btn-gold {
    background-color: var(--gold);
    border-color: var(--gold);
    color: var(--dark-bg);
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gold:hover {
    background-color: var(--gold-dark);
    border-color: var(--gold-dark);
    color: var(--dark-bg);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
}

.btn-mystical {
    background: linear-gradient(45deg, var(--purple-deep), var(--purple-light));
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-mystical:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 20, 140, 0.4);
}

/* Navigation */
.navbar-dark .navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--text-light);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--gold);
}

.dropdown-menu-dark {
    background-color: var(--dark-tertiary);
    border: 1px solid var(--gold);
}

.dropdown-menu-dark .dropdown-item {
    color: var(--text-light);
    transition: all 0.3s ease;
}

.dropdown-menu-dark .dropdown-item:hover {
    background-color: var(--gold);
    color: var(--dark-bg);
}

/* Cards */
.card-dark {
    background-color: var(--dark-secondary);
    border: 1px solid var(--dark-tertiary);
    color: var(--text-light);
    transition: all 0.3s ease;
}

.card-dark:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
    border-color: var(--gold);
}

.card-mystical {
    background: linear-gradient(135deg, var(--dark-secondary) 0%, var(--purple-deep) 100%);
    border: 1px solid var(--purple-light);
    color: var(--text-light);
}

/* Hero Section - TataMystik */
.hero-section-tatamystik {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-top: -80px;
    padding-top: 50px;
    margin-bottom: 0;
    padding-bottom: 0;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0,0,0,0.8) 0%,
        rgba(74, 20, 140, 0.6) 50%,
        rgba(0,0,0,0.9) 100%
    );
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: left;
    margin-top: -80px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.9);
    line-height: 1.2;
    animation: mysticalPulse 3s ease-in-out infinite;
}

@keyframes mysticalPulse {
    0%, 100% {
        text-shadow: 3px 3px 6px rgba(0,0,0,0.9), 0 0 20px rgba(212, 175, 55, 0.3);
    }
    50% {
        text-shadow: 3px 3px 6px rgba(0,0,0,0.9), 0 0 30px rgba(212, 175, 55, 0.6);
    }
}

.brand-name {
    font-size: 4rem;
    background: linear-gradient(45deg, var(--gold), var(--gold-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    margin-bottom: 0.5rem;
    text-shadow: none;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.8));
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    color: var(--text-light);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    font-weight: 600;
    letter-spacing: 0.5px;
    line-height: 1.4;
}

.hero-image-container {
    position: relative;
    z-index: 3;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.mystical-image-frame {
    position: relative;
    width: 400px;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.5),
        0 0 30px rgba(212, 175, 55, 0.3);
    animation: mysticalFloat 6s ease-in-out infinite;
}

.hero-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 20px;
}

.mystical-border-effect {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid transparent;
    border-radius: 23px;
    background: linear-gradient(45deg, var(--gold), var(--purple-light), var(--gold), var(--purple-light));
    background-size: 400% 400%;
    animation: mysticalBorderGlow 4s ease-in-out infinite;
    z-index: -1;
}

@keyframes mysticalBorderGlow {
    0%, 100% {
        background-position: 0% 50%;
        box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    }
    50% {
        background-position: 100% 50%;
        box-shadow: 0 0 40px rgba(212, 175, 55, 0.8);
    }
}

.mystical-circle {
    width: 200px;
    height: 200px;
    border: 3px solid var(--gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(10px);
    animation: mysticalRotate 20s linear infinite;
    position: relative;
}

.mystical-circle::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--gold), var(--purple-light), var(--gold));
    background-clip: border-box;
    z-index: -1;
    animation: mysticalRotate 15s linear infinite reverse;
}

@keyframes mysticalRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Legacy Hero Section (keep for other pages) */
.hero-section {
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23d4af37" stop-opacity="0.1"/><stop offset="100%" stop-color="%23000" stop-opacity="0.9"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23a)"/></svg>');
    background-size: cover;
    background-position: center;
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-section .hero-content {
    text-align: center;
    z-index: 2;
}

.hero-section .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.hero-section .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: var(--text-muted);
}

/* Subscription Cards */
.subscription-card {
    background: var(--dark-secondary);
    border: 2px solid var(--dark-tertiary);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.subscription-card.featured {
    border-color: var(--gold);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
}

.subscription-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.2);
}

.subscription-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gold);
    margin: 1rem 0;
}

.subscription-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.subscription-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--dark-tertiary);
}

.subscription-features li:last-child {
    border-bottom: none;
}

.subscription-features li i {
    color: var(--gold);
    margin-right: 0.5rem;
}

/* Course Cards */
.course-card {
    background: var(--dark-secondary);
    border: 1px solid var(--dark-tertiary);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.2);
    border-color: var(--gold);
}

.course-image {
    height: 200px;
    background: linear-gradient(45deg, var(--purple-deep), var(--blue-mystical));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--gold);
}

.course-level {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.level-standard { background-color: var(--silver); color: var(--dark-bg); }
.level-premium { background-color: var(--gold); color: var(--dark-bg); }
.level-gold { background-color: var(--purple-light); color: white; }

/* Forms */
.form-control-dark {
    background-color: var(--dark-tertiary);
    border: 1px solid var(--dark-tertiary);
    color: var(--text-light);
}

.form-control-dark:focus {
    background-color: var(--dark-tertiary);
    border-color: var(--gold);
    color: var(--text-light);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.form-control-dark::placeholder {
    color: var(--text-muted);
}

/* Alerts */
.alert-success {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: #4caf50;
    color: #81c784;
}

.alert-danger {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: #f44336;
    color: #e57373;
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.2);
    border-color: #2196f3;
    color: #64b5f6;
}

/* Mystical Elements */
.mystical-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--gold), var(--purple-light), var(--gold)) 1;
}

.glow-effect {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes mysticalFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .brand-name {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-content {
        text-align: center;
        margin-bottom: 2rem;
    }

    .mystical-circle {
        width: 150px;
        height: 150px;
    }

    .mystical-circle i {
        font-size: 3rem !important;
    }

    .hero-character-image {
        max-width: 320px;
        max-height: calc(100vh - 120px);
    }

    .subscription-card.featured {
        transform: none;
    }

    .hero-section-tatamystik {
        min-height: 100vh;
        padding: 2rem 0;
    }

    .hero-section-tatamystik .row {
        min-height: auto;
    }
}

/* Main content spacing */
.main-content {
    padding-top: 80px;
    min-height: calc(100vh - 200px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--gold);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gold-light);
}

/* Remove gap after hero section */
.hero-section-tatamystik + section {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Hero Character Image */
.hero-visual {
    position: relative;
    z-index: 3;
}

.hero-character-container {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 100vh;
    padding: 0;
    margin-bottom: 0;
}

.hero-character-image {
    max-width: 650px;
    max-height: calc(100vh - 30px);
    width: auto;
    height: calc(100vh - 30px);
    object-fit: contain;
    object-position: bottom;
    filter: drop-shadow(0 10px 30px rgba(0,0,0,0.5))
            drop-shadow(0 0 8px rgba(212, 175, 55, 0.15));
    animation: mysticalFloat 6s ease-in-out infinite;
}

#!/usr/bin/env python3
"""
Script pour télécharger les images des cartes de tarot
"""

import os
import requests
from urllib.parse import urlparse
import time

# C<PERSON><PERSON> le dossier s'il n'existe pas
os.makedirs('static/images/tarot', exist_ok=True)

# Liste des cartes de tarot avec leurs URLs (<PERSON><PERSON><PERSON><PERSON><PERSON> deck - domaine public)
tarot_cards = [
    # Arcanes Majeurs
    {"id": "00", "name": "le-mat", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/RWS_Tarot_00_Fool.jpg/200px-RWS_Tarot_00_Fool.jpg"},
    {"id": "01", "name": "le-bateleur", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/RWS_Tarot_01_Magician.jpg/200px-RWS_Tarot_01_Magician.jpg"},
    {"id": "02", "name": "la-papesse", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/RWS_Tarot_02_High_Priestess.jpg/200px-RWS_Tarot_02_High_Priestess.jpg"},
    {"id": "03", "name": "l-imperatrice", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d2/RWS_Tarot_03_Empress.jpg/200px-RWS_Tarot_03_Empress.jpg"},
    {"id": "04", "name": "l-empereur", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/RWS_Tarot_04_Emperor.jpg/200px-RWS_Tarot_04_Emperor.jpg"},
    {"id": "05", "name": "le-pape", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/8d/RWS_Tarot_05_Hierophant.jpg/200px-RWS_Tarot_05_Hierophant.jpg"},
    {"id": "06", "name": "l-amoureux", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/RWS_Tarot_06_Lovers.jpg/200px-RWS_Tarot_06_Lovers.jpg"},
    {"id": "07", "name": "le-chariot", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/9/9b/RWS_Tarot_07_Chariot.jpg/200px-RWS_Tarot_07_Chariot.jpg"},
    {"id": "08", "name": "la-justice", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/e/e0/RWS_Tarot_08_Strength.jpg/200px-RWS_Tarot_08_Strength.jpg"},
    {"id": "09", "name": "l-hermite", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/4/4d/RWS_Tarot_09_Hermit.jpg/200px-RWS_Tarot_09_Hermit.jpg"},
    {"id": "10", "name": "la-roue-de-fortune", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3c/RWS_Tarot_10_Wheel_of_Fortune.jpg/200px-RWS_Tarot_10_Wheel_of_Fortune.jpg"},
    {"id": "11", "name": "la-force", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f5/RWS_Tarot_11_Justice.jpg/200px-RWS_Tarot_11_Justice.jpg"},
    {"id": "12", "name": "le-pendu", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/RWS_Tarot_12_Hanged_Man.jpg/200px-RWS_Tarot_12_Hanged_Man.jpg"},
    {"id": "13", "name": "l-arcane-sans-nom", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d7/RWS_Tarot_13_Death.jpg/200px-RWS_Tarot_13_Death.jpg"},
    {"id": "14", "name": "temperance", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f8/RWS_Tarot_14_Temperance.jpg/200px-RWS_Tarot_14_Temperance.jpg"},
    {"id": "15", "name": "le-diable", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/5/55/RWS_Tarot_15_Devil.jpg/200px-RWS_Tarot_15_Devil.jpg"},
    {"id": "16", "name": "la-maison-dieu", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/5/53/RWS_Tarot_16_Tower.jpg/200px-RWS_Tarot_16_Tower.jpg"},
    {"id": "17", "name": "l-etoile", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/db/RWS_Tarot_17_Star.jpg/200px-RWS_Tarot_17_Star.jpg"},
    {"id": "18", "name": "la-lune", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/7/7f/RWS_Tarot_18_Moon.jpg/200px-RWS_Tarot_18_Moon.jpg"},
    {"id": "19", "name": "le-soleil", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/1/17/RWS_Tarot_19_Sun.jpg/200px-RWS_Tarot_19_Sun.jpg"},
    {"id": "20", "name": "le-jugement", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/RWS_Tarot_20_Judgement.jpg/200px-RWS_Tarot_20_Judgement.jpg"},
    {"id": "21", "name": "le-monde", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/f/ff/RWS_Tarot_21_World.jpg/200px-RWS_Tarot_21_World.jpg"},
    
    # Coupes (Cups)
    {"id": "cups-01", "name": "as-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/Cups01.jpg/200px-Cups01.jpg"},
    {"id": "cups-02", "name": "deux-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f8/Cups02.jpg/200px-Cups02.jpg"},
    {"id": "cups-03", "name": "trois-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Cups03.jpg/200px-Cups03.jpg"},
    {"id": "cups-04", "name": "quatre-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Cups04.jpg/200px-Cups04.jpg"},
    {"id": "cups-05", "name": "cinq-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d7/Cups05.jpg/200px-Cups05.jpg"},
    {"id": "cups-06", "name": "six-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/1/17/Cups06.jpg/200px-Cups06.jpg"},
    {"id": "cups-07", "name": "sept-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/a/ae/Cups07.jpg/200px-Cups07.jpg"},
    {"id": "cups-08", "name": "huit-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/6/60/Cups08.jpg/200px-Cups08.jpg"},
    {"id": "cups-09", "name": "neuf-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Cups09.jpg/200px-Cups09.jpg"},
    {"id": "cups-10", "name": "dix-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/84/Cups10.jpg/200px-Cups10.jpg"},
    {"id": "cups-11", "name": "valet-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Cups11.jpg/200px-Cups11.jpg"},
    {"id": "cups-12", "name": "cavalier-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/Cups12.jpg/200px-Cups12.jpg"},
    {"id": "cups-13", "name": "reine-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/Cups13.jpg/200px-Cups13.jpg"},
    {"id": "cups-14", "name": "roi-de-coupe", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/Cups14.jpg/200px-Cups14.jpg"},
    
    # Épées (Swords)
    {"id": "swords-01", "name": "as-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Swords01.jpg/200px-Swords01.jpg"},
    {"id": "swords-02", "name": "deux-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/9/9e/Swords02.jpg/200px-Swords02.jpg"},
    {"id": "swords-03", "name": "trois-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Swords03.jpg/200px-Swords03.jpg"},
    {"id": "swords-04", "name": "quatre-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/b/bf/Swords04.jpg/200px-Swords04.jpg"},
    {"id": "swords-05", "name": "cinq-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/23/Swords05.jpg/200px-Swords05.jpg"},
    {"id": "swords-06", "name": "six-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/29/Swords06.jpg/200px-Swords06.jpg"},
    {"id": "swords-07", "name": "sept-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/34/Swords07.jpg/200px-Swords07.jpg"},
    {"id": "swords-08", "name": "huit-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/Swords08.jpg/200px-Swords08.jpg"},
    {"id": "swords-09", "name": "neuf-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Swords09.jpg/200px-Swords09.jpg"},
    {"id": "swords-10", "name": "dix-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d4/Swords10.jpg/200px-Swords10.jpg"},
    {"id": "swords-11", "name": "valet-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/4/4c/Swords11.jpg/200px-Swords11.jpg"},
    {"id": "swords-12", "name": "cavalier-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Swords12.jpg/200px-Swords12.jpg"},
    {"id": "swords-13", "name": "reine-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d4/Swords13.jpg/200px-Swords13.jpg"},
    {"id": "swords-14", "name": "roi-d-epee", "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/33/Swords14.jpg/200px-Swords14.jpg"},
]

def download_card(card):
    """Télécharge une carte de tarot avec User-Agent"""
    try:
        filename = f"{card['id']}-{card['name']}.jpg"
        filepath = os.path.join('static/images/tarot', filename)

        # Vérifier si le fichier existe déjà
        if os.path.exists(filepath):
            print(f"✓ {filename} existe déjà")
            return True

        print(f"Téléchargement de {filename}...")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(card['url'], headers=headers, timeout=10)
        response.raise_for_status()

        with open(filepath, 'wb') as f:
            f.write(response.content)

        print(f"✓ {filename} téléchargé avec succès")
        return True

    except Exception as e:
        print(f"✗ Erreur lors du téléchargement de {card['name']}: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔮 Téléchargement des cartes de tarot...")
    print(f"📁 Dossier de destination: static/images/tarot")
    print(f"📊 Nombre de cartes à télécharger: {len(tarot_cards)}")
    print("-" * 50)
    
    success_count = 0
    failed_count = 0
    
    for i, card in enumerate(tarot_cards, 1):
        print(f"[{i}/{len(tarot_cards)}] ", end="")
        
        if download_card(card):
            success_count += 1
        else:
            failed_count += 1
            
        # Pause pour éviter de surcharger le serveur
        time.sleep(0.5)
    
    print("-" * 50)
    print(f"✅ Téléchargement terminé!")
    print(f"📈 Succès: {success_count}")
    print(f"📉 Échecs: {failed_count}")
    print(f"📊 Total: {len(tarot_cards)}")

if __name__ == "__main__":
    main()

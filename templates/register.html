{% extends "base.html" %}

{% block title %}Inscription - Temple Du Voile{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary text-center">
                    <h2 class="mystical-font text-gold mb-0">
                        <i class="fas fa-star"></i> Rejoindre Temple Du Voile
                    </h2>
                    <p class="text-muted mt-2">Commencez votre initiation aux mystères ésotériques</p>
                </div>
                
                <div class="card-body p-5">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label text-gold">
                                        <i class="fas fa-user"></i> Nom d'utilisateur
                                    </label>
                                    <input type="text" class="form-control form-control-dark" 
                                           id="username" name="username" required
                                           placeholder="Votre nom d'initié">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label text-gold">
                                        <i class="fas fa-envelope"></i> Adresse email
                                    </label>
                                    <input type="email" class="form-control form-control-dark" 
                                           id="email" name="email" required
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label text-gold">
                                <i class="fas fa-lock"></i> Mot de passe
                            </label>
                            <input type="password" class="form-control form-control-dark" 
                                   id="password" name="password" required
                                   placeholder="Choisissez un mot de passe sécurisé">
                        </div>
                        
                        <!-- Subscription Level Selection -->
                        <div class="mb-4">
                            <label class="form-label text-gold mb-3">
                                <i class="fas fa-crown"></i> Choisissez votre niveau d'initiation
                            </label>
                            
                            <div class="row g-3">
                                {% for level, details in subscription_levels.items() %}
                                <div class="col-md-4">
                                    <div class="card card-dark h-100">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="subscription_level" 
                                                   id="{{ level }}" value="{{ level }}" 
                                                   {% if level == 'premium' %}checked{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="{{ level }}">
                                                <h6 class="mystical-font text-gold mb-2">{{ details.name }}</h6>
                                                <div class="h5 text-gold mb-2">{{ "%.0f"|format(details.price) }}€/mois</div>
                                                <small class="text-muted">
                                                    {% if level == 'standard' %}
                                                        Parfait pour débuter
                                                    {% elif level == 'premium' %}
                                                        Le plus populaire
                                                    {% else %}
                                                        Accès complet
                                                    {% endif %}
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i> 
                                    Vous pourrez modifier votre abonnement à tout moment depuis votre tableau de bord.
                                </small>
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label text-muted" for="terms">
                                    J'accepte les <a href="#terms" class="text-gold">Conditions Générales d'Utilisation</a> 
                                    et la <a href="#privacy" class="text-gold">Politique de Confidentialité</a>
                                </label>
                            </div>
                            
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="newsletter">
                                <label class="form-check-label text-muted" for="newsletter">
                                    Je souhaite recevoir les actualités ésotériques et les nouveaux cours
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg">
                                <i class="fas fa-star"></i> Commencer Mon Initiation
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <p class="text-muted">
                            Déjà membre ? 
                            <a href="{{ url_for('login') }}" class="text-gold">Connectez-vous ici</a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Subscription Benefits -->
            <div class="row mt-5">
                <div class="col-12">
                    <h3 class="mystical-font text-gold text-center mb-4">
                        Pourquoi Rejoindre L'Arcanum Éveillé ?
                    </h3>
                </div>
                
                {% for level, details in subscription_levels.items() %}
                <div class="col-md-4 mb-4">
                    <div class="card card-dark h-100">
                        <div class="card-header bg-dark-tertiary text-center">
                            <h5 class="mystical-font text-gold mb-0">{{ details.name }}</h5>
                            <div class="h4 text-gold mt-2">{{ "%.0f"|format(details.price) }}€<small>/mois</small></div>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                {% for feature in details.features %}
                                <li class="mb-2">
                                    <i class="fas fa-check text-gold me-2"></i>
                                    {{ feature }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Security Notice -->
            <div class="alert alert-info mt-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-shield-alt fa-2x text-gold me-3"></i>
                    <div>
                        <h6 class="alert-heading">Sécurité et Confidentialité</h6>
                        <p class="mb-0">
                            Vos données personnelles et spirituelles sont protégées par un chiffrement de niveau bancaire. 
                            Nous respectons votre vie privée et ne partageons jamais vos informations.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add visual feedback for subscription selection
document.querySelectorAll('input[name="subscription_level"]').forEach(radio => {
    radio.addEventListener('change', function() {
        // Remove selected class from all cards
        document.querySelectorAll('.subscription-card').forEach(card => {
            card.classList.remove('border-gold');
        });
        
        // Add selected class to chosen card
        if (this.checked) {
            this.closest('.card').classList.add('border-gold');
        }
    });
});

// Set initial selection
document.querySelector('input[name="subscription_level"]:checked').closest('.card').classList.add('border-gold');
</script>
{% endblock %}

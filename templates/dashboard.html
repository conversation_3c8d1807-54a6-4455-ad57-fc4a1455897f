{% extends "base.html" %}

{% block title %}Tableau de Bord - TataMystik{% endblock %}

{% block content %}
<div class="container">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-mystical">
                <div class="card-body text-center py-4">
                    <h1 class="mystical-font text-gold mb-2">
                        <i class="fas fa-eye"></i> Bienvenue, {{ current_user.username }}
                    </h1>
                    <p class="lead text-light">
                        Votre sanctuaire personnel dans TataMystik
                    </p>
                    <div class="row justify-content-center mt-3">
                        <div class="col-auto">
                            <span class="badge bg-gold text-dark fs-6 px-3 py-2">
                                <i class="fas fa-crown"></i> Membre {{ current_user.subscription_level.title() }}
                            </span>
                        </div>
                        {% if current_user.subscription_expires %}
                        <div class="col-auto">
                            <span class="badge bg-secondary fs-6 px-3 py-2">
                                <i class="fas fa-calendar"></i> 
                                Expire le {{ current_user.subscription_expires.strftime('%d/%m/%Y') }}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <a href="{{ url_for('courses') }}" class="text-decoration-none">
                <div class="card card-dark h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-book-open fa-3x text-gold mb-3"></i>
                        <h5 class="mystical-font">Explorer les Cours</h5>
                        <p class="text-muted">Découvrez de nouveaux enseignements</p>
                    </div>
                </div>
            </a>
        </div>
        
        <div class="col-md-3 mb-3">
            <a href="{{ url_for('esoteric_tools') }}" class="text-decoration-none">
                <div class="card card-dark h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-magic fa-3x text-purple mb-3"></i>
                        <h5 class="mystical-font">Outils Ésotériques</h5>
                        <p class="text-muted">Tarot, rêves, horoscope</p>
                    </div>
                </div>
            </a>
        </div>
        
        <div class="col-md-3 mb-3">
            <a href="#community" class="text-decoration-none">
                <div class="card card-dark h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-silver mb-3"></i>
                        <h5 class="mystical-font">Communauté</h5>
                        <p class="text-muted">Échangez avec d'autres initiés</p>
                    </div>
                </div>
            </a>
        </div>
        
        <div class="col-md-3 mb-3">
            <a href="#marketplace" class="text-decoration-none">
                <div class="card card-dark h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-store fa-3x text-gold mb-3"></i>
                        <h5 class="mystical-font">Marketplace</h5>
                        <p class="text-muted">Objets et services ésotériques</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <div class="row">
        <!-- My Courses -->
        <div class="col-lg-8 mb-4">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h4 class="mystical-font text-gold mb-0">
                        <i class="fas fa-graduation-cap"></i> Mes Cours
                    </h4>
                </div>
                <div class="card-body">
                    {% if user_courses %}
                        <div class="row g-3">
                            {% for course in user_courses %}
                            <div class="col-md-6">
                                <div class="card card-dark border-secondary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mystical-font text-gold">{{ course.title }}</h6>
                                            <span class="badge level-{{ course.level }}">{{ course.level.title() }}</span>
                                        </div>
                                        <p class="text-muted small">{{ course.description[:80] }}...</p>
                                        
                                        <!-- Progress Bar -->
                                        <div class="mb-2">
                                            <div class="d-flex justify-content-between">
                                                <small class="text-muted">Progression</small>
                                                <small class="text-gold">75%</small>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar bg-gold" style="width: 75%"></div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between">
                                            <a href="{{ url_for('course_player', course_id=course.id) }}" 
                                               class="btn btn-outline-gold btn-sm">
                                                <i class="fas fa-play"></i> Continuer
                                            </a>
                                            <small class="text-muted align-self-center">
                                                <i class="fas fa-clock"></i> Dernière activité: il y a 2j
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="{{ url_for('courses') }}" class="btn btn-outline-gold">
                                <i class="fas fa-plus"></i> Découvrir Plus de Cours
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun cours inscrit</h5>
                            <p class="text-muted">Commencez votre parcours d'apprentissage dès maintenant</p>
                            <a href="{{ url_for('courses') }}" class="btn btn-gold">
                                <i class="fas fa-search"></i> Explorer les Cours
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4">
            <!-- Recent Dreams -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-purple mb-0">
                        <i class="fas fa-moon"></i> Rêves Récents
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_dreams %}
                        {% for dream in recent_dreams %}
                        <div class="border-bottom border-secondary pb-2 mb-2">
                            <small class="text-muted">{{ dream.created_at.strftime('%d/%m/%Y') }}</small>
                            <p class="mb-1 small">{{ dream.dream_description[:60] }}...</p>
                            <a href="{{ url_for('dream_interpretation') }}" class="text-purple small">
                                Voir l'interprétation
                            </a>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-moon fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">Aucun rêve interprété</p>
                            <a href="{{ url_for('dream_interpretation') }}" class="btn btn-outline-purple btn-sm">
                                Interpréter un Rêve
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Tarot -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-magic"></i> Tirages Tarot
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_tarot %}
                        {% for reading in recent_tarot %}
                        <div class="border-bottom border-secondary pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">{{ reading.created_at.strftime('%d/%m/%Y') }}</small>
                                <span class="badge bg-gold text-dark small">{{ reading.reading_type }}</span>
                            </div>
                            <p class="mb-1 small">{{ reading.interpretation[:50] }}...</p>
                            <a href="{{ url_for('tarot_reading') }}" class="text-gold small">
                                Voir le tirage
                            </a>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-magic fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">Aucun tirage effectué</p>
                            <a href="{{ url_for('tarot_reading') }}" class="btn btn-outline-gold btn-sm">
                                Tirer les Cartes
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Subscription Info -->
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-silver mb-0">
                        <i class="fas fa-crown"></i> Mon Abonnement
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="badge bg-gold text-dark fs-6 mb-3">
                            {{ current_user.subscription_level.title() }}
                        </div>
                        
                        {% if current_user.subscription_level != 'gold' %}
                        <p class="text-muted small mb-3">
                            Débloquez plus de fonctionnalités avec un abonnement supérieur
                        </p>
                        <a href="#upgrade" class="btn btn-outline-gold btn-sm">
                            <i class="fas fa-arrow-up"></i> Améliorer
                        </a>
                        {% else %}
                        <p class="text-gold small">
                            <i class="fas fa-star"></i> Vous avez accès à toutes les fonctionnalités !
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-mystical {
    background: linear-gradient(135deg, var(--purple-deep) 0%, var(--blue-mystical) 100%);
    border: 1px solid var(--gold);
}

.progress-bar.bg-gold {
    background-color: var(--gold) !important;
}

.btn-outline-purple {
    border-color: var(--purple-light);
    color: var(--purple-light);
}

.btn-outline-purple:hover {
    background-color: var(--purple-light);
    color: white;
}
</style>
{% endblock %}

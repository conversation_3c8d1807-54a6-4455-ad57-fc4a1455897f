{% extends "base.html" %}

{% block title %}{{ course.title }} - L'Arcanum Éveillé{% endblock %}

{% block content %}
<div class="container">
    <!-- Course Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}" class="text-gold">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('courses') }}" class="text-gold">Cours</a></li>
                    <li class="breadcrumb-item active text-muted">{{ course.title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Course Content -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="course-hero">
                    <div class="course-image-large">
                        {% if course.category == 'Sciences Occultes' %}
                            <i class="fas fa-eye fa-5x text-gold"></i>
                        {% elif course.category == 'Parapsychologie' %}
                            <i class="fas fa-brain fa-5x text-purple"></i>
                        {% elif course.category == 'Ésotérisme Africain' %}
                            <i class="fas fa-globe-africa fa-5x text-gold"></i>
                        {% elif course.category == 'Tarot et Divination' %}
                            <i class="fas fa-magic fa-5x text-gold"></i>
                        {% else %}
                            <i class="fas fa-book-open fa-5x text-gold"></i>
                        {% endif %}
                    </div>
                    <div class="course-overlay">
                        <span class="course-level level-{{ course.level }}">
                            {{ course.level.title() }}
                        </span>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <div class="mb-3">
                        <span class="badge bg-secondary">{{ course.category }}</span>
                        {% if is_enrolled %}
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-check"></i> Inscrit
                        </span>
                        {% endif %}
                    </div>
                    
                    <h1 class="mystical-font text-gold mb-3">{{ course.title }}</h1>
                    
                    <div class="course-meta mb-4">
                        <div class="row text-muted">
                            <div class="col-md-6">
                                <i class="fas fa-user"></i> 
                                <strong>Instructeur:</strong> {{ course.instructor or 'Expert Arcanum' }}
                            </div>
                            <div class="col-md-6">
                                <i class="fas fa-calendar"></i> 
                                <strong>Créé le:</strong> {{ course.created_at.strftime('%d/%m/%Y') }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="course-description mb-4">
                        <h3 class="mystical-font text-gold mb-3">Description</h3>
                        <p class="text-light">{{ course.description }}</p>
                    </div>
                    
                    <!-- Course Modules -->
                    <div class="course-curriculum mb-4">
                        <h3 class="mystical-font text-gold mb-3">
                            <i class="fas fa-list"></i> Programme du Cours
                        </h3>
                        
                        {% if course.modules %}
                        <div class="accordion" id="courseModules">
                            {% for module in course.modules %}
                            <div class="accordion-item bg-dark-tertiary border-secondary">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed bg-dark-tertiary text-light" 
                                            type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#module{{ module.id }}">
                                        <i class="fas fa-folder-open text-gold me-2"></i>
                                        {{ module.title }}
                                        <span class="badge bg-gold text-dark ms-auto me-2">
                                            {{ module.lessons|length }} leçons
                                        </span>
                                    </button>
                                </h2>
                                <div id="module{{ module.id }}" class="accordion-collapse collapse" 
                                     data-bs-parent="#courseModules">
                                    <div class="accordion-body">
                                        <p class="text-muted mb-3">{{ module.description }}</p>
                                        
                                        {% if module.lessons %}
                                        <ul class="list-unstyled">
                                            {% for lesson in module.lessons %}
                                            <li class="mb-2 p-2 border-start border-gold">
                                                <i class="fas fa-play-circle text-gold me-2"></i>
                                                {{ lesson.title }}
                                                {% if is_enrolled %}
                                                <span class="badge bg-success ms-2">
                                                    <i class="fas fa-unlock"></i>
                                                </span>
                                                {% else %}
                                                <span class="badge bg-secondary ms-2">
                                                    <i class="fas fa-lock"></i>
                                                </span>
                                                {% endif %}
                                            </li>
                                            {% endfor %}
                                        </ul>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-construction fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Le contenu du cours est en cours de préparation...</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- What You'll Learn -->
                    <div class="learning-objectives mb-4">
                        <h3 class="mystical-font text-gold mb-3">
                            <i class="fas fa-graduation-cap"></i> Ce que vous apprendrez
                        </h3>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-gold me-2"></i>
                                Les fondements théoriques de {{ course.category.lower() }}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-gold me-2"></i>
                                Les pratiques et techniques essentielles
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-gold me-2"></i>
                                L'application pratique des connaissances
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-gold me-2"></i>
                                Les aspects éthiques et spirituels
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Enrollment Card -->
            <div class="card card-dark mb-4">
                <div class="card-body text-center">
                    {% if current_user.is_authenticated %}
                        {% if is_enrolled %}
                            <div class="mb-3">
                                <i class="fas fa-check-circle fa-3x text-success mb-2"></i>
                                <h5 class="text-success">Vous êtes inscrit !</h5>
                            </div>
                            <a href="{{ url_for('course_player', course_id=course.id) }}" 
                               class="btn btn-gold btn-lg w-100">
                                <i class="fas fa-play"></i> Commencer le Cours
                            </a>
                        {% elif can_access %}
                            <div class="mb-3">
                                <i class="fas fa-star fa-3x text-gold mb-2"></i>
                                <h5 class="text-gold">Prêt à commencer ?</h5>
                            </div>
                            <a href="{{ url_for('enroll_course', course_id=course.id) }}" 
                               class="btn btn-gold btn-lg w-100">
                                <i class="fas fa-plus"></i> S'inscrire au Cours
                            </a>
                        {% else %}
                            <div class="mb-3">
                                <i class="fas fa-lock fa-3x text-muted mb-2"></i>
                                <h5 class="text-muted">Niveau {{ course.level.title() }} requis</h5>
                                <p class="text-muted small">
                                    Ce cours nécessite un abonnement {{ course.level.title() }} ou supérieur.
                                </p>
                            </div>
                            <a href="#upgrade" class="btn btn-outline-gold w-100">
                                <i class="fas fa-arrow-up"></i> Améliorer mon Abonnement
                            </a>
                        {% endif %}
                    {% else %}
                        <div class="mb-3">
                            <i class="fas fa-user-plus fa-3x text-gold mb-2"></i>
                            <h5 class="text-gold">Rejoignez-nous !</h5>
                            <p class="text-muted small">
                                Créez un compte pour accéder à ce cours et bien plus encore.
                            </p>
                        </div>
                        <a href="{{ url_for('register') }}" class="btn btn-gold w-100 mb-2">
                            <i class="fas fa-user-plus"></i> S'inscrire
                        </a>
                        <a href="{{ url_for('login') }}" class="btn btn-outline-gold w-100">
                            <i class="fas fa-sign-in-alt"></i> Se connecter
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Course Stats -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-chart-bar"></i> Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Étudiants inscrits</span>
                            <span class="text-gold">{{ course.enrollments|length }}</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Modules</span>
                            <span class="text-gold">{{ course.modules|length }}</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Leçons totales</span>
                            <span class="text-gold">
                                {% set total_lessons = course.modules|sum(attribute='lessons')|length %}
                                {{ total_lessons }}
                            </span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Niveau</span>
                            <span class="level-{{ course.level }}">{{ course.level.title() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Courses -->
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-book"></i> Cours Similaires
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        Découvrez d'autres cours dans la catégorie {{ course.category }}
                    </p>
                    <a href="{{ url_for('courses', category=course.category) }}" 
                       class="btn btn-outline-gold btn-sm w-100">
                        <i class="fas fa-search"></i> Explorer {{ course.category }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.course-hero {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, var(--purple-deep) 0%, var(--blue-mystical) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px 10px 0 0;
}

.course-image-large {
    text-align: center;
}

.course-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
}

.accordion-button {
    background-color: var(--dark-tertiary) !important;
    color: var(--text-light) !important;
    border: none !important;
}

.accordion-button:not(.collapsed) {
    background-color: var(--dark-secondary) !important;
    color: var(--gold) !important;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(212, 175, 55, 0.25);
}

.stat-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--dark-tertiary);
}

.stat-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

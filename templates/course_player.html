{% extends "base.html" %}

{% block title %}{{ course.title }} - Lecteur - TataMystik{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Course Navigation Sidebar -->
        <div class="col-lg-3 bg-dark-secondary p-0">
            <div class="course-sidebar">
                <div class="sidebar-header p-3 bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-1">{{ course.title }}</h5>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-gold" style="width: {{ enrollment.progress or 0 }}%"></div>
                    </div>
                    <small class="text-muted">Progression: {{ "%.0f"|format(enrollment.progress or 0) }}%</small>
                </div>
                
                <div class="sidebar-content">
                    {% for module in course.modules %}
                    <div class="module-section">
                        <div class="module-header p-3 bg-dark-tertiary border-bottom border-secondary">
                            <h6 class="text-gold mb-0">
                                <i class="fas fa-folder-open me-2"></i>
                                {{ module.title }}
                            </h6>
                        </div>
                        
                        {% for lesson in module.lessons %}
                        <div class="lesson-item p-3 border-bottom border-secondary cursor-pointer" 
                             onclick="loadLesson({{ lesson.id }})">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-play-circle text-gold me-2"></i>
                                <span class="lesson-title">{{ lesson.title }}</span>
                                <i class="fas fa-check-circle text-success ms-auto" style="display: none;"></i>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                </div>
                
                <div class="sidebar-footer p-3 bg-dark-tertiary">
                    <a href="{{ url_for('course_detail', course_id=course.id) }}" 
                       class="btn btn-outline-gold btn-sm w-100">
                        <i class="fas fa-arrow-left"></i> Retour au Cours
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="col-lg-9 p-0">
            <div class="course-player">
                <!-- Player Header -->
                <div class="player-header p-3 bg-dark-tertiary border-bottom border-secondary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mystical-font text-gold mb-0" id="currentLessonTitle">
                                Sélectionnez une leçon pour commencer
                            </h4>
                            <small class="text-muted" id="currentModuleTitle"></small>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="player-controls">
                                <button class="btn btn-outline-gold btn-sm me-2" onclick="previousLesson()">
                                    <i class="fas fa-step-backward"></i> Précédent
                                </button>
                                <button class="btn btn-outline-gold btn-sm" onclick="nextLesson()">
                                    Suivant <i class="fas fa-step-forward"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="player-content p-4" id="lessonContent">
                    <div class="welcome-screen text-center py-5">
                        <i class="fas fa-play-circle fa-5x text-gold mb-4"></i>
                        <h2 class="mystical-font text-gold mb-3">Bienvenue dans {{ course.title }}</h2>
                        <p class="lead text-muted mb-4">
                            Sélectionnez une leçon dans le menu de gauche pour commencer votre apprentissage.
                        </p>
                        <div class="course-info">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <div class="card card-dark">
                                        <div class="card-body">
                                            <h5 class="mystical-font text-gold">Informations du Cours</h5>
                                            <ul class="list-unstyled">
                                                <li class="mb-2">
                                                    <i class="fas fa-folder text-gold me-2"></i>
                                                    <strong>Modules:</strong> {{ course.modules|length }}
                                                </li>
                                                <li class="mb-2">
                                                    <i class="fas fa-play text-gold me-2"></i>
                                                    <strong>Leçons:</strong> 
                                                    {% set total_lessons = course.modules|sum(attribute='lessons')|length %}
                                                    {{ total_lessons }}
                                                </li>
                                                <li class="mb-2">
                                                    <i class="fas fa-user text-gold me-2"></i>
                                                    <strong>Instructeur:</strong> {{ course.instructor or 'Expert Arcanum' }}
                                                </li>
                                                <li>
                                                    <i class="fas fa-layer-group text-gold me-2"></i>
                                                    <strong>Niveau:</strong> {{ course.level.title() }}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="notes-section p-3 bg-dark-secondary border-top border-secondary" style="display: none;" id="notesSection">
                    <h6 class="text-gold mb-3">
                        <i class="fas fa-sticky-note"></i> Mes Notes
                    </h6>
                    <textarea class="form-control form-control-dark" 
                              id="lessonNotes" 
                              rows="4" 
                              placeholder="Prenez des notes sur cette leçon..."></textarea>
                    <div class="mt-2">
                        <button class="btn btn-outline-gold btn-sm" onclick="saveNotes()">
                            <i class="fas fa-save"></i> Sauvegarder
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.course-sidebar {
    height: calc(100vh - 80px);
    overflow-y: auto;
    position: sticky;
    top: 80px;
}

.course-player {
    height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
}

.player-content {
    flex: 1;
    overflow-y: auto;
    background: var(--dark-bg);
}

.lesson-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.lesson-item:hover {
    background-color: var(--dark-tertiary);
    border-left: 3px solid var(--gold);
}

.lesson-item.active {
    background-color: var(--gold);
    color: var(--dark-bg);
}

.lesson-item.active .text-gold {
    color: var(--dark-bg) !important;
}

.lesson-item.completed {
    background-color: rgba(40, 167, 69, 0.1);
}

.lesson-item.completed .fa-check-circle {
    display: inline-block !important;
}

.welcome-screen {
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.video-player {
    width: 100%;
    height: 400px;
    background: var(--dark-tertiary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.lesson-content {
    max-width: 800px;
    margin: 0 auto;
}

.lesson-content h1, .lesson-content h2, .lesson-content h3 {
    color: var(--gold);
    font-family: 'Cinzel', serif;
}

.lesson-content p {
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.lesson-content ul, .lesson-content ol {
    margin-bottom: 1.5rem;
}

.lesson-content li {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .course-sidebar {
        height: auto;
        position: relative;
    }
    
    .course-player {
        height: auto;
    }
    
    .player-controls {
        margin-top: 1rem;
    }
}
</style>

<script>
let currentLessonId = null;
let lessons = [];

// Initialize lessons data
{% for module in course.modules %}
    {% for lesson in module.lessons %}
    lessons.push({
        id: {{ lesson.id }},
        title: "{{ lesson.title }}",
        moduleTitle: "{{ module.title }}",
        content: `{{ lesson.content|safe }}`,
        videoUrl: "{{ lesson.video_url or '' }}"
    });
    {% endfor %}
{% endfor %}

function loadLesson(lessonId) {
    const lesson = lessons.find(l => l.id === lessonId);
    if (!lesson) return;
    
    currentLessonId = lessonId;
    
    // Update UI
    document.getElementById('currentLessonTitle').textContent = lesson.title;
    document.getElementById('currentModuleTitle').textContent = lesson.moduleTitle;
    
    // Update sidebar
    document.querySelectorAll('.lesson-item').forEach(item => {
        item.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // Load content
    const contentArea = document.getElementById('lessonContent');
    contentArea.innerHTML = `
        <div class="lesson-content">
            ${lesson.videoUrl ? `
                <div class="video-player mb-4">
                    <i class="fas fa-play-circle fa-4x text-gold"></i>
                    <p class="text-muted mt-2">Lecteur vidéo (${lesson.videoUrl})</p>
                </div>
            ` : ''}
            
            <h1 class="mystical-font text-gold mb-4">${lesson.title}</h1>
            
            <div class="lesson-text">
                ${lesson.content || '<p class="text-muted">Contenu de la leçon en cours de préparation...</p>'}
            </div>
            
            <div class="lesson-actions mt-4">
                <button class="btn btn-gold" onclick="markAsCompleted(${lessonId})">
                    <i class="fas fa-check"></i> Marquer comme Terminé
                </button>
                <button class="btn btn-outline-gold ms-2" onclick="toggleNotes()">
                    <i class="fas fa-sticky-note"></i> Prendre des Notes
                </button>
            </div>
        </div>
    `;
    
    // Show notes section
    document.getElementById('notesSection').style.display = 'block';
    
    // Update progress
    updateProgress();
}

function markAsCompleted(lessonId) {
    const lessonItem = document.querySelector(`[onclick="loadLesson(${lessonId})"]`);
    lessonItem.classList.add('completed');
    
    // Update progress
    updateProgress();
    
    // Show success message
    ArcanumUtils.showNotification('Leçon marquée comme terminée !', 'success');
}

function updateProgress() {
    const totalLessons = lessons.length;
    const completedLessons = document.querySelectorAll('.lesson-item.completed').length;
    const progress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
    
    document.querySelector('.progress-bar').style.width = progress + '%';
    document.querySelector('.sidebar-header small').textContent = `Progression: ${Math.round(progress)}%`;
    
    // Update server
    ArcanumUtils.updateCourseProgress({{ course.id }}, currentLessonId);
}

function previousLesson() {
    if (!currentLessonId) return;
    
    const currentIndex = lessons.findIndex(l => l.id === currentLessonId);
    if (currentIndex > 0) {
        const prevLesson = lessons[currentIndex - 1];
        const lessonElement = document.querySelector(`[onclick="loadLesson(${prevLesson.id})"]`);
        lessonElement.click();
    }
}

function nextLesson() {
    if (!currentLessonId) return;
    
    const currentIndex = lessons.findIndex(l => l.id === currentLessonId);
    if (currentIndex < lessons.length - 1) {
        const nextLesson = lessons[currentIndex + 1];
        const lessonElement = document.querySelector(`[onclick="loadLesson(${nextLesson.id})"]`);
        lessonElement.click();
    }
}

function toggleNotes() {
    const notesSection = document.getElementById('notesSection');
    const isVisible = notesSection.style.display !== 'none';
    notesSection.style.display = isVisible ? 'none' : 'block';
    
    if (!isVisible) {
        document.getElementById('lessonNotes').focus();
    }
}

function saveNotes() {
    const notes = document.getElementById('lessonNotes').value;
    // Here you would typically save to the server
    ArcanumUtils.showNotification('Notes sauvegardées !', 'success');
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                previousLesson();
                break;
            case 'ArrowRight':
                e.preventDefault();
                nextLesson();
                break;
            case 's':
                e.preventDefault();
                saveNotes();
                break;
        }
    }
});

// Auto-load first lesson if available
document.addEventListener('DOMContentLoaded', function() {
    if (lessons.length > 0) {
        // Auto-select first lesson after a short delay
        setTimeout(() => {
            const firstLessonElement = document.querySelector('.lesson-item');
            if (firstLessonElement) {
                firstLessonElement.click();
            }
        }, 1000);
    }
});
</script>
{% endblock %}

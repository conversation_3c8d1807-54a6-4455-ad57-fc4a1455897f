{% extends "base.html" %}

{% block title %}Cours - TataMystik{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-book-open"></i> Bibliothèque des Savoirs
                </h1>
                <p class="lead text-muted">
                    Explorez nos cours exclusifs sur les sciences occultes, la parapsychologie et l'ésotérisme africain
                </p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="search" class="form-label text-gold">
                                <i class="fas fa-search"></i> Rechercher
                            </label>
                            <input type="text" class="form-control form-control-dark" 
                                   id="search" name="search" 
                                   value="{{ request.args.get('search', '') }}"
                                   placeholder="Titre, description, instructeur...">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="category" class="form-label text-gold">
                                <i class="fas fa-tags"></i> Catégorie
                            </label>
                            <select class="form-select form-control-dark" id="category" name="category">
                                <option value="">Toutes les catégories</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}" {% if request.args.get('category') == cat %}selected{% endif %}>
                                    {{ cat }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="level" class="form-label text-gold">
                                <i class="fas fa-layer-group"></i> Niveau
                            </label>
                            <select class="form-select form-control-dark" id="level" name="level">
                                <option value="">Tous les niveaux</option>
                                <option value="standard" {% if request.args.get('level') == 'standard' %}selected{% endif %}>
                                    Standard
                                </option>
                                <option value="premium" {% if request.args.get('level') == 'premium' %}selected{% endif %}>
                                    Premium
                                </option>
                                <option value="gold" {% if request.args.get('level') == 'gold' %}selected{% endif %}>
                                    Gold
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-gold w-100">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex flex-wrap gap-2 justify-content-center">
                <a href="{{ url_for('courses') }}" 
                   class="btn {% if not request.args.get('category') %}btn-gold{% else %}btn-outline-gold{% endif %} btn-sm">
                    Tous
                </a>
                <a href="{{ url_for('courses', category='Sciences Occultes') }}" 
                   class="btn {% if request.args.get('category') == 'Sciences Occultes' %}btn-gold{% else %}btn-outline-gold{% endif %} btn-sm">
                    <i class="fas fa-eye"></i> Sciences Occultes
                </a>
                <a href="{{ url_for('courses', category='Parapsychologie') }}" 
                   class="btn {% if request.args.get('category') == 'Parapsychologie' %}btn-gold{% else %}btn-outline-gold{% endif %} btn-sm">
                    <i class="fas fa-brain"></i> Parapsychologie
                </a>
                <a href="{{ url_for('courses', category='Ésotérisme Africain') }}" 
                   class="btn {% if request.args.get('category') == 'Ésotérisme Africain' %}btn-gold{% else %}btn-outline-gold{% endif %} btn-sm">
                    <i class="fas fa-globe-africa"></i> Ésotérisme Africain
                </a>
                <a href="{{ url_for('courses', category='Tarot et Divination') }}" 
                   class="btn {% if request.args.get('category') == 'Tarot et Divination' %}btn-gold{% else %}btn-outline-gold{% endif %} btn-sm">
                    <i class="fas fa-magic"></i> Tarot & Divination
                </a>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row g-4">
        {% if courses %}
            {% for course in courses %}
            <div class="col-lg-4 col-md-6">
                <div class="course-card">
                    <div class="position-relative">
                        <div class="course-image">
                            {% if course.category == 'Sciences Occultes' %}
                                <i class="fas fa-eye"></i>
                            {% elif course.category == 'Parapsychologie' %}
                                <i class="fas fa-brain"></i>
                            {% elif course.category == 'Ésotérisme Africain' %}
                                <i class="fas fa-globe-africa"></i>
                            {% elif course.category == 'Tarot et Divination' %}
                                <i class="fas fa-magic"></i>
                            {% else %}
                                <i class="fas fa-book-open"></i>
                            {% endif %}
                        </div>
                        <span class="course-level level-{{ course.level }}">
                            {{ course.level.title() }}
                        </span>
                        
                        {% if current_user.is_authenticated %}
                            {% set enrollment = current_user.enrollments|selectattr('course_id', 'equalto', course.id)|first %}
                            {% if enrollment %}
                            <div class="position-absolute top-0 start-0 m-2">
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i> Inscrit
                                </span>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="mb-2">
                            <span class="badge bg-secondary small">{{ course.category }}</span>
                        </div>
                        
                        <h5 class="card-title mystical-font text-gold">{{ course.title }}</h5>
                        <p class="card-text text-muted">{{ course.description[:120] }}...</p>
                        
                        <div class="course-meta mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user"></i> {{ course.instructor or 'Expert Arcanum' }}
                            </small>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> {{ course.created_at.strftime('%d/%m/%Y') }}
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            {% if current_user.is_authenticated %}
                                {% set enrollment = current_user.enrollments|selectattr('course_id', 'equalto', course.id)|first %}
                                {% if enrollment %}
                                    <a href="{{ url_for('course_player', course_id=course.id) }}" 
                                       class="btn btn-gold btn-sm">
                                        <i class="fas fa-play"></i> Continuer
                                    </a>
                                {% elif current_user.has_access_to_course(course) %}
                                    <a href="{{ url_for('enroll_course', course_id=course.id) }}" 
                                       class="btn btn-outline-gold btn-sm">
                                        <i class="fas fa-plus"></i> S'inscrire
                                    </a>
                                {% else %}
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="fas fa-lock"></i> {{ course.level.title() }} requis
                                    </button>
                                {% endif %}
                            {% else %}
                                <a href="{{ url_for('login') }}" class="btn btn-outline-gold btn-sm">
                                    <i class="fas fa-sign-in-alt"></i> Se connecter
                                </a>
                            {% endif %}
                            
                            <a href="{{ url_for('course_detail', course_id=course.id) }}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-info-circle"></i> Détails
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun cours trouvé</h4>
                    <p class="text-muted">
                        Essayez de modifier vos critères de recherche ou explorez toutes nos catégories.
                    </p>
                    <a href="{{ url_for('courses') }}" class="btn btn-gold">
                        <i class="fas fa-refresh"></i> Voir Tous les Cours
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination (if needed) -->
    {% if courses|length >= 12 %}
    <div class="row mt-5">
        <div class="col-12">
            <nav aria-label="Navigation des cours">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <span class="page-link bg-dark-secondary border-secondary text-muted">Précédent</span>
                    </li>
                    <li class="page-item active">
                        <span class="page-link bg-gold border-gold text-dark">1</span>
                    </li>
                    <li class="page-item">
                        <a class="page-link bg-dark-secondary border-secondary text-light" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link bg-dark-secondary border-secondary text-light" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link bg-dark-secondary border-secondary text-light" href="#">Suivant</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    <!-- Call to Action -->
    {% if not current_user.is_authenticated %}
    <div class="row mt-5">
        <div class="col-12">
            <div class="card card-mystical text-center">
                <div class="card-body py-4">
                    <h3 class="mystical-font text-gold mb-3">
                        Prêt à Commencer Votre Initiation ?
                    </h3>
                    <p class="lead text-light mb-4">
                        Rejoignez TataMystik et accédez à tous nos cours exclusifs
                    </p>
                    <a href="{{ url_for('register') }}" class="btn btn-gold btn-lg me-3">
                        <i class="fas fa-star"></i> Rejoindre Maintenant
                    </a>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-gold btn-lg">
                        <i class="fas fa-sign-in-alt"></i> Se Connecter
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.course-image {
    background: linear-gradient(45deg, var(--purple-deep), var(--blue-mystical));
}

.form-select.form-control-dark {
    background-color: var(--dark-tertiary);
    border: 1px solid var(--dark-tertiary);
    color: var(--text-light);
}

.form-select.form-control-dark:focus {
    background-color: var(--dark-tertiary);
    border-color: var(--gold);
    color: var(--text-light);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.form-select.form-control-dark option {
    background-color: var(--dark-tertiary);
    color: var(--text-light);
}
</style>
{% endblock %}

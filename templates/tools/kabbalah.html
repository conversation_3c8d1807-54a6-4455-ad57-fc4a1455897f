{% extends "base.html" %}

{% block title %}Chemin de Vie Kabbalistique - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-star-of-david"></i> Chemin de Vie Kabbalistique
                </h1>
                <p class="lead text-muted">
                    Découvrez votre nombre de destinée selon la tradition hébraïque ancestrale
                </p>
                <div class="kabbalah-decoration">
                    <span class="hebrew-symbols">אבגדהוזחטיכלמנסעפצקרשת</span>
                </div>
            </div>

            <!-- Consultation Form -->
            <div class="card card-dark mb-5">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0 text-center">
                        <i class="fas fa-scroll"></i> Calcul Kabbalistique
                    </h3>
                    <p class="text-center text-muted mb-0 mt-2">
                        Révélez votre destinée à travers les lettres sacrées hébraïques
                    </p>
                </div>
                <div class="card-body">
                    <form id="kabbalisticForm" method="POST">
                        <!-- Client Name -->
                        <div class="mb-4">
                            <label for="client_name" class="form-label text-gold">
                                <i class="fas fa-user-circle"></i> Nom du Consultant
                            </label>
                            <input type="text" class="form-control bg-dark-secondary text-light border-gold" 
                                   id="client_name" name="client_name" 
                                   placeholder="Entrez votre nom ou celui du consultant"
                                   required>
                        </div>

                        <!-- Full Name -->
                        <div class="mb-4">
                            <label for="full_name" class="form-label text-gold">
                                <i class="fas fa-signature"></i> Nom Complet (Prénom + Nom de famille)
                            </label>
                            <input type="text" class="form-control bg-dark-secondary text-light border-gold" 
                                   id="full_name" name="full_name" 
                                   placeholder="Nom complet tel qu'inscrit à l'état civil"
                                   required>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> 
                                Utilisez votre nom de naissance complet pour un calcul précis
                            </div>
                        </div>

                        <!-- Birth Date -->
                        <div class="mb-4">
                            <label for="birth_date" class="form-label text-gold">
                                <i class="fas fa-calendar-alt"></i> Date de Naissance
                            </label>
                            <input type="date" class="form-control bg-dark-secondary text-light border-gold" 
                                   id="birth_date" name="birth_date" required>
                        </div>

                        <!-- Kabbalah Information -->
                        <div class="alert alert-info bg-dark-secondary border-gold mb-4">
                            <h6 class="text-gold mb-3">
                                <i class="fas fa-book-open"></i> La Tradition Kabbalistique
                            </h6>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="kabbalah-info p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-language"></i> Lettres Hébraïques
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Chaque lettre possède une valeur numérique sacrée</li>
                                            <li>Votre nom révèle votre essence spirituelle</li>
                                            <li>Tradition millénaire de la Kabbale</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="kabbalah-info p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-infinity"></i> Nombre de Destinée
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Révèle votre mission spirituelle</li>
                                            <li>Guide vos choix de vie importants</li>
                                            <li>Dévoile vos talents cachés</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                <h6 class="text-warning">
                                    <i class="fas fa-star-of-david"></i> Sagesse Ancestrale
                                </h6>
                                <p class="text-light small mb-0">
                                    "Chaque âme descend sur terre avec une mission spécifique. 
                                    Votre nom, donné par l'Univers, contient les clés de cette mission sacrée. 
                                    La Kabbale révèle ces secrets à travers la science des nombres et des lettres."
                                </p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg" id="calculateBtn">
                                <i class="fas fa-calculator"></i> Calculer mon Chemin Kabbalistique
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Readings -->
            {% if readings %}
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-history"></i> Vos Calculs Kabbalistiques
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% for reading in readings %}
                        <div class="col-md-6">
                            <div class="reading-item p-3 rounded border border-gold">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="text-gold mb-1">
                                            <i class="fas fa-star-of-david"></i> 
                                            {{ reading.client_name }} - Nombre {{ reading.destiny_number }}
                                        </h6>
                                        <small class="text-muted">
                                            {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </small>
                                    </div>
                                    <span class="badge bg-gold text-dark">
                                        <i class="fas fa-scroll"></i> Kabbale
                                    </span>
                                </div>
                                <p class="text-light small mb-2">
                                    <strong>Nom analysé:</strong> {{ reading.full_name }}
                                </p>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-gold btn-sm" 
                                            onclick="showKabbalisticDetails({{ reading.id }})">
                                        <i class="fas fa-eye"></i> Détails
                                    </button>
                                    <a href="{{ url_for('download_kabbalah_pdf', reading_id=reading.id) }}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-download"></i> PDF
                                    </a>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteKabbalisticReading({{ reading.id }}, '{{ reading.client_name }}')">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="kabbalisticModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-primary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title text-gold">
                    <i class="fas fa-star-of-david"></i> Détails du Calcul Kabbalistique
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="kabbalisticModalBody">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<style>
.kabbalah-decoration {
    margin: 20px 0;
}

.hebrew-symbols {
    font-size: 1.5rem;
    color: var(--gold);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    letter-spacing: 3px;
    font-family: 'Times New Roman', serif;
}

.kabbalah-info {
    border-left: 4px solid var(--gold);
    transition: all 0.3s ease;
}

.kabbalah-info:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.btn-loading {
    position: relative;
    overflow: hidden;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.border-gold {
    border-color: var(--gold) !important;
}
</style>

<script>
// Form submission with Hebrew calculation animation
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('kabbalisticForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const button = document.getElementById('calculateBtn');
        const originalText = button.innerHTML;
        
        button.classList.add('btn-loading');
        button.innerHTML = '<i class="fas fa-calculator"></i> Conversion en lettres hébraïques...';
        button.disabled = true;
        
        // Simulate calculation
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-star-of-david"></i> Calcul du nombre de destinée...';
        }, 2000);
        
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-scroll"></i> Analyse kabbalistique en cours...';
        }, 4000);
        
        setTimeout(() => {
            // Submit the form
            this.submit();
        }, 6000);
    });
});

function showKabbalisticDetails(readingId) {
    const modalBody = document.getElementById('kabbalisticModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement du calcul kabbalistique...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('kabbalisticModal'));
    modal.show();
    
    // Load reading details via AJAX
    fetch(`/api/kabbalah-reading/${readingId}`)
        .then(response => response.json())
        .then(data => {
            modalBody.innerHTML = `
                <div class="reading-details">
                    <div class="text-center mb-4">
                        <div class="destiny-number-display p-3 bg-dark-tertiary rounded">
                            <h6 class="text-gold mb-2">Nombre de Destinée Kabbalistique</h6>
                            <div class="destiny-number" style="font-size: 3rem; color: var(--gold); font-weight: bold;">
                                ${data.destiny_number}
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-gold text-dark">${data.client_name}</span>
                            <span class="badge bg-purple text-light ms-2">${data.full_name}</span>
                        </div>
                    </div>
                    
                    <h6 class="text-gold mb-3">Calcul Hébraïque</h6>
                    <div class="calculation-content p-3 bg-dark-tertiary rounded mb-4">
                        <div class="text-light">${data.hebrew_calculation}</div>
                    </div>
                    
                    <h6 class="text-gold mb-3">Analyse du Chemin de Vie</h6>
                    <div class="analysis-content p-3 bg-dark-secondary rounded mb-4">
                        <div class="text-light">${data.life_path_analysis}</div>
                    </div>
                    
                    <h6 class="text-purple mb-3">Guidance Spirituelle</h6>
                    <div class="guidance-content p-3 bg-dark-tertiary rounded">
                        <div class="text-light">${data.spiritual_guidance}</div>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>Erreur lors du chargement des détails</p>
                </div>
            `;
        });
}

function deleteKabbalisticReading(readingId, clientName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le calcul kabbalistique de ${clientName} ?`)) {
        fetch(`/api/kabbalah-reading/${readingId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            alert('Erreur lors de la suppression');
        });
    }
}
</script>
{% endblock %}

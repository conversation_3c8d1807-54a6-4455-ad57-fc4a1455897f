{% extends "base.html" %}

{% block title %}Lecture de Tarot - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-magic"></i> Votre Lecture de Tarot
                </h1>
                <p class="lead text-muted">
                    Les cartes révèlent les mystères de votre destinée
                </p>
            </div>

            <!-- Question Card -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-purple mb-0">
                        <i class="fas fa-question-circle"></i> Votre Question
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-light lead">{{ reading.question }}</p>
                </div>
            </div>

            <!-- Cards Drawn -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-cards-blank"></i> Cartes Tirées
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        {% set cards = reading.cards_drawn | from_json %}
                        {% for card in cards %}
                        <div class="col-md-4">
                            <div class="tarot-card-result text-center">
                                <div class="card-visual mb-3">
                                    <img src="{{ url_for('static', filename='images/tarot/' + card.image) }}"
                                         alt="{{ card.name }}"
                                         class="tarot-card-img">
                                    <div class="card-overlay">
                                        <h6 class="card-name text-gold">{{ card.name }}</h6>
                                        <span class="card-id">{{ card.id }}</span>
                                    </div>
                                </div>
                                <p class="card-meaning text-muted small">{{ card.meaning }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Interpretation -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-eye"></i> Interprétation Experte
                    </h5>
                </div>
                <div class="card-body">
                    <div class="interpretation-content">
                        <div class="interpretation-text p-4 rounded">
                            {{ reading.interpretation|clean_html|nl2br|safe }}
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-dark-secondary rounded">
                        <h6 class="text-warning">
                            <i class="fas fa-compass"></i> Guidance Spirituelle
                        </h6>
                        <ul class="text-muted mb-0">
                            <li>Méditez sur le message des cartes dans les prochains jours</li>
                            <li>Observez les synchronicités qui se manifestent</li>
                            <li>Gardez l'esprit ouvert aux opportunités</li>
                            <li>Faites confiance à votre intuition</li>
                            <li>Notez vos ressentis et insights dans un journal</li>
                            <li>Laissez les énergies des arcanes vous guider</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="text-center">
                <a href="{{ url_for('download_tarot_pdf', reading_id=reading.id) }}" class="btn btn-gold me-3">
                    <i class="fas fa-download"></i> Télécharger PDF
                </a>
                <a href="{{ url_for('tarot_reading') }}" class="btn btn-purple me-3">
                    <i class="fas fa-plus"></i> Nouvelle Lecture
                </a>
                <a href="{{ url_for('esoteric_tools') }}" class="btn btn-outline-gold">
                    <i class="fas fa-magic"></i> Autres Outils
                </a>
            </div>

            <!-- Date -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-calendar"></i> 
                    Lecture effectuée le {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.tarot-card-result {
    transition: all 0.3s ease;
}

.tarot-card-result:hover {
    transform: translateY(-5px);
}

.tarot-card-img {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: 12px;
    border: 3px solid var(--gold);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
    transition: all 0.3s ease;
}

.tarot-card-img:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.5);
}

.card-overlay {
    position: relative;
    margin-top: 10px;
}

.card-name {
    margin: 0;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
}

.card-id {
    display: inline-block;
    background: var(--gold);
    color: var(--dark-bg);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-top: 5px;
}

.card-meaning {
    font-style: italic;
    line-height: 1.4;
    margin-top: 10px;
}

.interpretation-content {
    position: relative;
}

.interpretation-text {
    background: linear-gradient(135deg, rgba(123, 31, 162, 0.1), rgba(75, 0, 130, 0.1));
    border-left: 4px solid var(--gold);
    position: relative;
    line-height: 1.8;
    font-size: 1.1rem;
}

.interpretation-text::before {
    content: '🔮';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.interpretation-content ul {
    list-style-type: none;
    padding-left: 0;
}

.interpretation-content li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(123, 31, 162, 0.2);
}

.interpretation-content li:last-child {
    border-bottom: none;
}

.interpretation-content li::before {
    content: '✨';
    margin-right: 10px;
    color: var(--gold);
}
</style>
{% endblock %}

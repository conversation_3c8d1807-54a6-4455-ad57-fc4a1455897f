{% extends "base.html" %}

{% block title %}Runes Nordiques - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-mountain"></i> Runes Nordiques
                </h1>
                <p class="lead text-muted">
                    Découvrez votre destinée à travers la sagesse ancestrale des Völva
                </p>
                <div class="nordic-decoration">
                    <span class="rune-symbol">ᚠᚢᚦᚨᚱᚲ</span>
                </div>
            </div>

            <!-- Consultation Form -->
            <div class="card card-dark mb-5">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0 text-center">
                        <i class="fas fa-fire"></i> Consultation Völva
                    </h3>
                    <p class="text-center text-muted mb-0 mt-2">
                        Laissez les runes révéler les secrets de votre destinée
                    </p>
                </div>
                <div class="card-body">
                    <form id="runeForm" method="POST">
                        <!-- Client Name -->
                        <div class="mb-4">
                            <label for="client_name" class="form-label text-gold">
                                <i class="fas fa-user-shield"></i> Nom du Consultant
                            </label>
                            <input type="text" class="form-control bg-dark-secondary text-light border-gold" 
                                   id="client_name" name="client_name" 
                                   placeholder="Entrez votre nom ou celui du consultant"
                                   required>
                        </div>

                        <!-- Question -->
                        <div class="mb-4">
                            <label for="question" class="form-label text-gold">
                                <i class="fas fa-scroll"></i> Votre Question aux Nornes
                            </label>
                            <textarea class="form-control bg-dark-secondary text-light border-gold" 
                                    id="question" name="question" rows="3" 
                                    placeholder="Posez votre question aux déesses du destin... Que souhaitez-vous savoir sur votre avenir, vos défis ou votre chemin spirituel ?"></textarea>
                        </div>

                        <!-- Reading Type -->
                        <div class="mb-4">
                            <label class="form-label text-gold">
                                <i class="fas fa-dice-d20"></i> Type de Tirage Runique
                            </label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="rune-option">
                                        <input type="radio" class="btn-check" name="reading_type" id="single_rune" value="single" checked>
                                        <label class="btn btn-outline-gold w-100 p-3" for="single_rune">
                                            <div class="rune-display mb-2">ᚠ</div>
                                            <div><strong>Rune Unique</strong></div>
                                            <small class="text-muted">Guidance immédiate</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="rune-option">
                                        <input type="radio" class="btn-check" name="reading_type" id="three_rune" value="three_rune">
                                        <label class="btn btn-outline-gold w-100 p-3" for="three_rune">
                                            <div class="rune-display mb-2">ᚠ ᚢ ᚦ</div>
                                            <div><strong>Trois Nornes</strong></div>
                                            <small class="text-muted">Passé, Présent, Futur</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="rune-option">
                                        <input type="radio" class="btn-check" name="reading_type" id="five_rune" value="five_rune">
                                        <label class="btn btn-outline-gold w-100 p-3" for="five_rune">
                                            <div class="rune-display mb-2">ᚠ ᚢ ᚦ ᚨ ᚱ</div>
                                            <div><strong>Croix d'Odin</strong></div>
                                            <small class="text-muted">Situation complexe</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="rune-option">
                                        <input type="radio" class="btn-check" name="reading_type" id="nine_rune" value="nine_rune">
                                        <label class="btn btn-outline-gold w-100 p-3" for="nine_rune">
                                            <div class="rune-display mb-2">ᚠᚢᚦᚨᚱᚲᚷᚹᚺ</div>
                                            <div><strong>Arbre Yggdrasil</strong></div>
                                            <small class="text-muted">Destinée complète</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Nordic Wisdom -->
                        <div class="alert alert-info bg-dark-secondary border-gold mb-4">
                            <h6 class="text-gold mb-3">
                                <i class="fas fa-tree"></i> Sagesse des Anciens
                            </h6>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="wisdom-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-eye"></i> Préparation Spirituelle
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Concentrez-vous sur votre question</li>
                                            <li>Respirez profondément et calmez votre esprit</li>
                                            <li>Invoquez la protection d'Odin</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="wisdom-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-moon"></i> Moment Propice
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Les runes parlent mieux dans le silence</li>
                                            <li>L'aube et le crépuscule sont favorables</li>
                                            <li>Écoutez votre intuition nordique</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                <h6 class="text-warning">
                                    <i class="fas fa-hammer"></i> Parole de Völva
                                </h6>
                                <p class="text-light small mb-0">
                                    "Les runes ne mentent jamais, mais elles parlent en symboles. 
                                    Ouvrez votre cœur à leur sagesse millénaire et laissez les Nornes 
                                    tisser le fil de votre destinée."
                                </p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg" id="castRunesBtn">
                                <i class="fas fa-fire"></i> Lancer les Runes Sacrées
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Readings -->
            {% if readings %}
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-scroll"></i> Vos Consultations Runiques
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% for reading in readings %}
                        <div class="col-md-6">
                            <div class="reading-item p-3 rounded border border-gold">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="text-gold mb-1">
                                            <i class="fas fa-mountain"></i> 
                                            {% if reading.client_name %}{{ reading.client_name }} - {% endif %}
                                            {% if reading.reading_type == 'single' %}Rune Unique{% endif %}
                                            {% if reading.reading_type == 'three_rune' %}Trois Nornes{% endif %}
                                            {% if reading.reading_type == 'five_rune' %}Croix d'Odin{% endif %}
                                            {% if reading.reading_type == 'nine_rune' %}Arbre Yggdrasil{% endif %}
                                        </h6>
                                        <small class="text-muted">
                                            {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </small>
                                    </div>
                                    <span class="badge bg-purple text-light">
                                        <i class="fas fa-rune"></i> Völva
                                    </span>
                                </div>
                                {% if reading.question %}
                                <p class="text-light small mb-2">
                                    <strong>Question:</strong> {{ reading.question[:100] }}{% if reading.question|length > 100 %}...{% endif %}
                                </p>
                                {% endif %}
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-gold btn-sm" 
                                            onclick="showRuneDetails({{ reading.id }})">
                                        <i class="fas fa-eye"></i> Détails
                                    </button>
                                    <a href="{{ url_for('download_rune_pdf', reading_id=reading.id) }}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-download"></i> PDF
                                    </a>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteRuneReading({{ reading.id }}, '{{ reading.client_name or 'cette consultation' }}')">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="runeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-primary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title text-gold">
                    <i class="fas fa-mountain"></i> Détails de la Consultation Runique
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="runeModalBody">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<style>
.nordic-decoration {
    margin: 20px 0;
}

.rune-symbol {
    font-size: 2rem;
    color: var(--gold);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    letter-spacing: 5px;
}

.rune-option {
    transition: all 0.3s ease;
}

.rune-option:hover {
    transform: translateY(-3px);
}

.rune-option input[type="radio"]:checked + label {
    background-color: var(--gold);
    color: var(--dark-bg);
    border-color: var(--gold);
}

.rune-display {
    font-size: 1.5rem;
    color: var(--gold);
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
    letter-spacing: 3px;
}

.wisdom-item {
    border-left: 4px solid var(--gold);
    transition: all 0.3s ease;
}

.wisdom-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.btn-loading {
    position: relative;
    overflow: hidden;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Nordic theme colors */
:root {
    --nordic-blue: #2c5aa0;
    --nordic-silver: #c0c0c0;
    --nordic-ice: #e6f3ff;
}

.border-gold {
    border-color: var(--gold) !important;
}

.bg-purple {
    background-color: var(--purple-deep) !important;
}
</style>

<script>
// Form submission with Nordic animation
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('runeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const button = document.getElementById('castRunesBtn');
        const originalText = button.innerHTML;
        
        button.classList.add('btn-loading');
        button.innerHTML = '<i class="fas fa-fire"></i> Les runes dansent dans le feu...';
        button.disabled = true;
        
        // Simulate rune casting
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-eye"></i> La Völva interprète les signes...';
        }, 2000);
        
        setTimeout(() => {
            // Submit the form
            this.submit();
        }, 4000);
    });
});

function showRuneDetails(readingId) {
    const modalBody = document.getElementById('runeModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement de la consultation runique...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('runeModal'));
    modal.show();
    
    // Load reading details via AJAX
    fetch(`/api/rune-reading/${readingId}`)
        .then(response => response.json())
        .then(data => {
            modalBody.innerHTML = `
                <div class="reading-details">
                    <div class="text-center mb-4">
                        <div class="rune-symbols-display p-3 bg-dark-tertiary rounded">
                            <h6 class="text-gold mb-2">Runes Tirées</h6>
                            <div class="runes-drawn" style="font-size: 2rem; color: var(--gold); letter-spacing: 10px;">
                                ${data.runes_symbols || 'ᚠᚢᚦ'}
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-gold text-dark">${data.client_name}</span>
                            <span class="badge bg-purple text-light ms-2">${data.reading_type_display}</span>
                        </div>
                    </div>
                    
                    ${data.question ? `
                    <h6 class="text-purple mb-3">Question aux Nornes</h6>
                    <div class="question-content p-3 bg-dark-tertiary rounded mb-4">
                        <p class="text-light">${data.question}</p>
                    </div>
                    ` : ''}
                    
                    <h6 class="text-gold mb-3">Analyse des Runes</h6>
                    <div class="analysis-content p-3 bg-dark-tertiary rounded mb-4">
                        <div class="text-light">${data.rune_analysis}</div>
                    </div>
                    
                    <h6 class="text-gold mb-3">Interprétation Spirituelle</h6>
                    <div class="interpretation-content p-3 bg-dark-secondary rounded mb-4">
                        <div class="text-light">${data.interpretation}</div>
                    </div>
                    
                    ${data.volva_guidance ? `
                    <h6 class="text-purple mb-3">Guidance de la Völva</h6>
                    <div class="guidance-content p-3 bg-dark-tertiary rounded">
                        <div class="text-light">${data.volva_guidance}</div>
                    </div>
                    ` : ''}
                </div>
            `;
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>Erreur lors du chargement des détails</p>
                </div>
            `;
        });
}

function deleteRuneReading(readingId, clientName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la consultation runique de ${clientName} ?`)) {
        fetch(`/api/rune-reading/${readingId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            alert('Erreur lors de la suppression');
        });
    }
}
</script>
{% endblock %}

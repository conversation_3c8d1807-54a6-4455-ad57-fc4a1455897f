{% extends "base.html" %}

{% block title %}Pendule Virtuel - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-circle-notch"></i> Pendule Virtuel
                </h1>
                <p class="lead text-muted">
                    Consultez l'oracle du pendule pour des réponses immédiates
                </p>
                <div class="pendulum-decoration">
                    <span class="pendulum-symbols">◯ ◐ ◑ ◒ ◓ ◔ ◕</span>
                </div>
            </div>

            <!-- Pendulum Interface -->
            <div class="card card-dark mb-5">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0 text-center">
                        <i class="fas fa-magic"></i> Consultation Radiesthésique
                    </h3>
                    <p class="text-center text-muted mb-0 mt-2">
                        Posez votre question et laissez le pendule vous guider
                    </p>
                </div>
                <div class="card-body">
                    <!-- Pendulum Animation Area -->
                    <div class="pendulum-container text-center mb-5">
                        <div class="pendulum-holder">
                            <div class="pendulum-string" id="pendulumString"></div>
                            <div class="pendulum-weight" id="pendulumWeight">
                                <i class="fas fa-gem"></i>
                            </div>
                        </div>
                        <div class="pendulum-board mt-4">
                            <div class="response-area">
                                <div class="response-option oui" data-response="oui">OUI</div>
                                <div class="response-option non" data-response="non">NON</div>
                                <div class="response-option peut-etre" data-response="peut-etre">PEUT-ÊTRE</div>
                            </div>
                        </div>
                    </div>

                    <!-- Question Form -->
                    <form id="pendulumForm" method="POST">
                        <!-- Client Name -->
                        <div class="mb-4">
                            <label for="client_name" class="form-label text-gold">
                                <i class="fas fa-user-circle"></i> Nom du Consultant
                            </label>
                            <input type="text" class="form-control bg-dark-secondary text-light border-gold" 
                                   id="client_name" name="client_name" 
                                   placeholder="Entrez votre nom ou celui du consultant"
                                   required>
                        </div>

                        <!-- Question -->
                        <div class="mb-4">
                            <label for="question" class="form-label text-gold">
                                <i class="fas fa-question-circle"></i> Votre Question
                            </label>
                            <textarea class="form-control bg-dark-secondary text-light border-gold" 
                                    id="question" name="question" rows="3" 
                                    placeholder="Posez une question claire qui peut être répondue par Oui, Non ou Peut-être..."
                                    required></textarea>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> 
                                Formulez une question précise pour une réponse claire du pendule
                            </div>
                        </div>

                        <!-- Pendulum Instructions -->
                        <div class="alert alert-info bg-dark-secondary border-gold mb-4">
                            <h6 class="text-gold mb-3">
                                <i class="fas fa-compass"></i> Guide de Radiesthésie
                            </h6>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="pendulum-info p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-heart"></i> Préparation
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Centrez-vous et respirez calmement</li>
                                            <li>Formulez votre question clairement</li>
                                            <li>Ouvrez votre cœur à la guidance</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="pendulum-info p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-eye"></i> Interprétation
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li><strong>OUI:</strong> Énergie positive, go ahead</li>
                                            <li><strong>NON:</strong> Résistance, reconsidérez</li>
                                            <li><strong>PEUT-ÊTRE:</strong> Timing incertain</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                <h6 class="text-warning">
                                    <i class="fas fa-circle-notch"></i> Sagesse du Pendule
                                </h6>
                                <p class="text-light small mb-0">
                                    "Le pendule amplifie votre intuition naturelle et vous connecte aux énergies subtiles. 
                                    Il révèle ce que votre âme sait déjà mais que votre mental n'a pas encore perçu."
                                </p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="button" class="btn btn-gold btn-lg" id="consultBtn">
                                <i class="fas fa-magic"></i> Consulter le Pendule
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Result Display -->
            <div id="resultCard" class="card card-dark" style="display: none;">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-crystal-ball"></i> Réponse du Pendule
                    </h3>
                </div>
                <div class="card-body" id="resultContent">
                    <!-- Result will be displayed here -->
                </div>
            </div>

            <!-- Recent Readings -->
            {% if readings %}
            <div class="card card-dark mt-5">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-history"></i> Vos Consultations Récentes
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% for reading in readings %}
                        <div class="col-md-6">
                            <div class="reading-item p-3 rounded border border-gold">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="text-gold mb-1">
                                            <i class="fas fa-circle-notch"></i> 
                                            {{ reading.client_name }}
                                        </h6>
                                        <small class="text-muted">
                                            {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </small>
                                    </div>
                                    <span class="badge 
                                        {% if reading.pendulum_response == 'oui' %}bg-success
                                        {% elif reading.pendulum_response == 'non' %}bg-danger
                                        {% else %}bg-warning{% endif %} text-dark">
                                        {{ reading.pendulum_response.upper() }}
                                    </span>
                                </div>
                                <p class="text-light small mb-2">
                                    <strong>Question:</strong> {{ reading.question[:80] }}{% if reading.question|length > 80 %}...{% endif %}
                                </p>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-gold btn-sm" 
                                            onclick="showPendulumDetails({{ reading.id }})">
                                        <i class="fas fa-eye"></i> Détails
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="deletePendulumReading({{ reading.id }}, '{{ reading.client_name }}')">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="pendulumModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-primary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title text-gold">
                    <i class="fas fa-circle-notch"></i> Détails de la Consultation
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="pendulumModalBody">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<style>
.pendulum-decoration {
    margin: 20px 0;
}

.pendulum-symbols {
    font-size: 2rem;
    color: var(--gold);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    letter-spacing: 8px;
}

.pendulum-container {
    height: 300px;
    position: relative;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.1), transparent);
    border-radius: 15px;
    padding: 20px;
}

.pendulum-holder {
    position: relative;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.pendulum-string {
    width: 2px;
    height: 150px;
    background: linear-gradient(to bottom, var(--gold), transparent);
    margin: 0 auto;
    transform-origin: top center;
    transition: transform 2s ease-in-out;
}

.pendulum-weight {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, var(--gold), #b8860b);
    border-radius: 50%;
    margin: -5px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-bg);
    font-size: 1.2rem;
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.7);
    transition: transform 2s ease-in-out;
}

.pendulum-board {
    margin-top: 30px;
}

.response-area {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.response-option {
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.response-option.oui {
    background: rgba(40, 167, 69, 0.2);
    border: 2px solid #28a745;
    color: #28a745;
}

.response-option.non {
    background: rgba(220, 53, 69, 0.2);
    border: 2px solid #dc3545;
    color: #dc3545;
}

.response-option.peut-etre {
    background: rgba(255, 193, 7, 0.2);
    border: 2px solid #ffc107;
    color: #ffc107;
}

.response-option.active {
    transform: scale(1.1);
    box-shadow: 0 0 20px currentColor;
}

.pendulum-info {
    border-left: 4px solid var(--gold);
    transition: all 0.3s ease;
}

.pendulum-info:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

/* Pendulum animations */
.swing-oui {
    transform: rotate(15deg);
}

.swing-non {
    transform: rotate(-15deg);
}

.swing-peut-etre {
    transform: rotate(0deg) scale(1.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const consultBtn = document.getElementById('consultBtn');
    const pendulumString = document.getElementById('pendulumString');
    const pendulumWeight = document.getElementById('pendulumWeight');
    const responseOptions = document.querySelectorAll('.response-option');
    const resultCard = document.getElementById('resultCard');
    const resultContent = document.getElementById('resultContent');

    consultBtn.addEventListener('click', function() {
        const clientName = document.getElementById('client_name').value;
        const question = document.getElementById('question').value;

        if (!clientName || !question) {
            alert('Veuillez remplir tous les champs');
            return;
        }

        // Start pendulum animation
        consultBtn.disabled = true;
        consultBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Le pendule se concentre...';

        // Clear previous active states
        responseOptions.forEach(opt => opt.classList.remove('active'));

        // Simulate pendulum movement
        setTimeout(() => {
            // Random swinging
            const swings = ['swing-oui', 'swing-non', 'swing-peut-etre'];
            let swingCount = 0;
            
            const swingInterval = setInterval(() => {
                const randomSwing = swings[Math.floor(Math.random() * swings.length)];
                pendulumString.className = randomSwing;
                pendulumWeight.className = 'pendulum-weight ' + randomSwing;
                swingCount++;
                
                if (swingCount >= 8) {
                    clearInterval(swingInterval);
                    
                    // Final decision
                    const responses = ['oui', 'non', 'peut-etre'];
                    const finalResponse = responses[Math.floor(Math.random() * responses.length)];
                    
                    // Animate to final position
                    setTimeout(() => {
                        pendulumString.className = 'swing-' + finalResponse;
                        pendulumWeight.className = 'pendulum-weight swing-' + finalResponse;
                        
                        // Highlight the chosen response
                        const chosenOption = document.querySelector(`.response-option.${finalResponse}`);
                        chosenOption.classList.add('active');
                        
                        // Submit the form with the response
                        setTimeout(() => {
                            submitPendulumReading(clientName, question, finalResponse);
                        }, 1000);
                        
                    }, 500);
                }
            }, 300);
            
        }, 1000);
    });

    function submitPendulumReading(clientName, question, response) {
        fetch('/tools/pendulum', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `client_name=${encodeURIComponent(clientName)}&question=${encodeURIComponent(question)}&pendulum_response=${response}`
        })
        .then(response => response.json())
        .then(data => {
            // Display result
            resultContent.innerHTML = `
                <div class="text-center mb-4">
                    <div class="pendulum-result p-4 bg-dark-tertiary rounded">
                        <h4 class="text-gold mb-3">Réponse du Pendule</h4>
                        <div class="response-display mb-3">
                            <span class="badge ${response === 'oui' ? 'bg-success' : response === 'non' ? 'bg-danger' : 'bg-warning'} text-dark" 
                                  style="font-size: 2rem; padding: 15px 30px;">
                                ${response.toUpperCase()}
                            </span>
                        </div>
                        <p class="text-light"><strong>Question:</strong> ${question}</p>
                    </div>
                </div>
                
                <h6 class="text-gold mb-3">Lecture Énergétique</h6>
                <div class="energy-content p-3 bg-dark-secondary rounded mb-4">
                    <div class="text-light">${data.energy_reading}</div>
                </div>
                
                <h6 class="text-purple mb-3">Guidance Complémentaire</h6>
                <div class="guidance-content p-3 bg-dark-tertiary rounded">
                    <div class="text-light">${data.guidance}</div>
                </div>
            `;
            
            resultCard.style.display = 'block';
            resultCard.scrollIntoView({ behavior: 'smooth' });
            
            // Reset button
            consultBtn.disabled = false;
            consultBtn.innerHTML = '<i class="fas fa-magic"></i> Consulter le Pendule';
            
            // Reset pendulum
            setTimeout(() => {
                pendulumString.className = '';
                pendulumWeight.className = 'pendulum-weight';
                responseOptions.forEach(opt => opt.classList.remove('active'));
            }, 3000);
            
        })
        .catch(error => {
            console.error('Error:', error);
            consultBtn.disabled = false;
            consultBtn.innerHTML = '<i class="fas fa-magic"></i> Consulter le Pendule';
        });
    }
});

function showPendulumDetails(readingId) {
    const modalBody = document.getElementById('pendulumModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement des détails...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('pendulumModal'));
    modal.show();
    
    // Load reading details via AJAX
    fetch(`/api/pendulum-reading/${readingId}`)
        .then(response => response.json())
        .then(data => {
            modalBody.innerHTML = `
                <div class="reading-details">
                    <div class="text-center mb-4">
                        <div class="pendulum-response p-3 bg-dark-tertiary rounded">
                            <h6 class="text-gold mb-2">Réponse du Pendule</h6>
                            <span class="badge ${data.pendulum_response === 'oui' ? 'bg-success' : data.pendulum_response === 'non' ? 'bg-danger' : 'bg-warning'} text-dark" 
                                  style="font-size: 1.5rem; padding: 10px 20px;">
                                ${data.pendulum_response.toUpperCase()}
                            </span>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-gold text-dark">${data.client_name}</span>
                        </div>
                    </div>
                    
                    <h6 class="text-purple mb-3">Question Posée</h6>
                    <div class="question-content p-3 bg-dark-tertiary rounded mb-4">
                        <p class="text-light">${data.question}</p>
                    </div>
                    
                    <h6 class="text-gold mb-3">Lecture Énergétique</h6>
                    <div class="energy-content p-3 bg-dark-secondary rounded mb-4">
                        <div class="text-light">${data.energy_reading}</div>
                    </div>
                    
                    <h6 class="text-purple mb-3">Guidance Complémentaire</h6>
                    <div class="guidance-content p-3 bg-dark-tertiary rounded">
                        <div class="text-light">${data.guidance}</div>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>Erreur lors du chargement des détails</p>
                </div>
            `;
        });
}

function deletePendulumReading(readingId, clientName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la consultation de ${clientName} ?`)) {
        fetch(`/api/pendulum-reading/${readingId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            alert('Erreur lors de la suppression');
        });
    }
}
</script>
{% endblock %}

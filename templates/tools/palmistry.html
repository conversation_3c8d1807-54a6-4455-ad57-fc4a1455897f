{% extends "base.html" %}

{% block title %}Chiromancie - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-hand-paper"></i> Lecture de la Paume
                </h1>
                <p class="lead text-muted">
                    Découvrez les secrets cachés dans les lignes de votre main
                </p>
                <div class="mt-3">
                    <span class="badge bg-gold text-dark px-3 py-2">
                        <i class="fas fa-eye"></i> Analyse par Vision Mystique Avancée
                    </span>
                </div>
            </div>

            <!-- Upload Form -->
            <div class="card card-dark mb-5">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0 text-center">
                        <i class="fas fa-camera"></i> Téléchargez votre Photo
                    </h3>
                </div>
                <div class="card-body">
                    <form id="palmistryForm" method="POST" enctype="multipart/form-data">
                        <!-- Client Name -->
                        <div class="mb-4">
                            <label for="client_name" class="form-label text-gold">
                                <i class="fas fa-user"></i> Nom du Client
                            </label>
                            <input type="text" class="form-control bg-dark-secondary text-light border-gold"
                                   id="client_name" name="client_name"
                                   placeholder="Entrez le nom du client (pour vous ou quelqu'un d'autre)"
                                   required>
                        </div>

                        <!-- Question -->
                        <div class="mb-4">
                            <label for="question" class="form-label text-gold">
                                <i class="fas fa-question-circle"></i> Question Spécifique (optionnel)
                            </label>
                            <textarea class="form-control bg-dark-secondary text-light border-gold"
                                    id="question" name="question" rows="3"
                                    placeholder="Que souhaitez-vous savoir sur votre avenir, votre personnalité ou votre destinée ?"></textarea>
                        </div>

                        <!-- Hand Type -->
                        <div class="mb-4">
                            <label class="form-label text-gold">
                                <i class="fas fa-hands"></i> Type de Main
                            </label>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="palm-option">
                                        <input type="radio" class="btn-check" name="hand_type" id="left_hand" value="left" checked>
                                        <label class="btn btn-outline-gold w-100 p-3" for="left_hand">
                                            <i class="fas fa-hand-paper fa-2x mb-2"></i>
                                            <div>Main Gauche</div>
                                            <small class="text-muted">Potentiel inné</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="palm-option">
                                        <input type="radio" class="btn-check" name="hand_type" id="right_hand" value="right">
                                        <label class="btn btn-outline-gold w-100 p-3" for="right_hand">
                                            <i class="fas fa-hand-paper fa-2x mb-2" style="transform: scaleX(-1);"></i>
                                            <div>Main Droite</div>
                                            <small class="text-muted">Réalisations actuelles</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="palm-option">
                                        <input type="radio" class="btn-check" name="hand_type" id="both_hands" value="both">
                                        <label class="btn btn-outline-gold w-100 p-3" for="both_hands">
                                            <i class="fas fa-hands fa-2x mb-2"></i>
                                            <div>Les Deux Mains</div>
                                            <small class="text-muted">Analyse complète</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="mb-4">
                            <label for="palm_image" class="form-label text-gold">
                                <i class="fas fa-upload"></i> Photo de la Paume
                            </label>
                            <div class="upload-area" id="uploadArea">
                                <input type="file" class="form-control d-none" id="palm_image" name="palm_image" 
                                       accept="image/*" required>
                                <div class="upload-content text-center">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-gold mb-3"></i>
                                    <h5 class="text-light">Cliquez pour sélectionner une image</h5>
                                    <p class="text-muted">ou glissez-déposez votre photo ici</p>
                                    <small class="text-muted">Formats acceptés: JPG, PNG, WEBP (max 10MB)</small>
                                </div>
                                <div class="preview-container d-none">
                                    <img id="imagePreview" src="" alt="Aperçu" class="img-fluid rounded">
                                    <button type="button" class="btn btn-sm btn-outline-danger mt-2" id="removeImage">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Instructions Détaillées -->
                        <div class="alert alert-info bg-dark-secondary border-gold mb-4">
                            <h6 class="text-gold mb-3">
                                <i class="fas fa-lightbulb"></i> Conseils pour une Lecture Optimale
                            </h6>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="advice-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-sun"></i> Éclairage
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Prenez la photo dans un endroit bien éclairé</li>
                                            <li>Utilisez la lumière naturelle si possible</li>
                                            <li>Évitez les éclairages trop directs ou trop faibles</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="advice-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-hand-paper"></i> Position de la Main
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Étalez bien votre main, paume vers l'appareil</li>
                                            <li>Gardez les doigts légèrement écartés</li>
                                            <li>Maintenez la main stable et détendue</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="advice-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-eye"></i> Visibilité des Lignes
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>Assurez-vous que les lignes sont bien visibles</li>
                                            <li>Évitez les ombres sur la paume</li>
                                            <li>Nettoyez votre main si nécessaire</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="advice-item p-3 bg-dark-tertiary rounded">
                                        <h6 class="text-warning">
                                            <i class="fas fa-camera"></i> Cadrage
                                        </h6>
                                        <ul class="text-light small mb-0">
                                            <li>La main doit occuper la majeure partie de l'image</li>
                                            <li>Centrez bien la paume dans le cadre</li>
                                            <li>Évitez les arrière-plans distrayants</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                <h6 class="text-warning">
                                    <i class="fas fa-star"></i> Conseil d'Expert
                                </h6>
                                <p class="text-light small mb-0">
                                    Pour une lecture optimale, prenez plusieurs photos sous différents angles
                                    et choisissez la plus nette. Une bonne photo est la clé d'une analyse précise !
                                </p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg" id="analyzeBtn">
                                <i class="fas fa-magic"></i> Analyser ma Paume
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Readings -->
            {% if readings %}
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-history"></i> Vos Lectures Récentes
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% for reading in readings %}
                        <div class="col-md-6">
                            <div class="reading-item p-3 rounded border border-gold">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="text-gold mb-1">
                                            <i class="fas fa-hand-paper"></i>
                                            {% if reading.client_name %}{{ reading.client_name }} - {% endif %}{{ reading.hand_type|title }}
                                        </h6>
                                        <small class="text-muted">
                                            {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </small>
                                    </div>
                                    {% if reading.confidence_score %}
                                    <span class="badge bg-gold text-dark">
                                        {{ "%.0f"|format(reading.confidence_score * 100) }}%
                                    </span>
                                    {% endif %}
                                </div>
                                {% if reading.question %}
                                <p class="text-light small mb-2">
                                    <strong>Question:</strong> {{ reading.question[:100] }}{% if reading.question|length > 100 %}...{% endif %}
                                </p>
                                {% endif %}
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-gold btn-sm"
                                            onclick="showReadingDetails({{ reading.id }})">
                                        <i class="fas fa-eye"></i> Détails
                                    </button>
                                    <a href="{{ url_for('download_palm_pdf', reading_id=reading.id) }}"
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-download"></i> PDF
                                    </a>
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="deleteReading({{ reading.id }}, '{{ reading.client_name or 'cette lecture' }}')">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="readingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-primary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title text-gold">
                    <i class="fas fa-hand-paper"></i> Détails de la Lecture
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="readingModalBody">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<style>
.palm-option {
    transition: all 0.3s ease;
}

.palm-option:hover {
    transform: translateY(-3px);
}

.palm-option input[type="radio"]:checked + label {
    background-color: var(--gold);
    color: var(--dark-bg);
    border-color: var(--gold);
}

.upload-area {
    border: 2px dashed var(--gold);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(212, 175, 55, 0.1));
}

.upload-area:hover {
    border-color: #ffd700;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.15));
}

.upload-area.dragover {
    border-color: #ffd700;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.2));
    transform: scale(1.02);
}

#imagePreview {
    max-height: 300px;
    border: 2px solid var(--gold);
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.btn-loading {
    position: relative;
    overflow: hidden;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
</style>

<script>
// Image upload handling
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('palm_image');
    const imagePreview = document.getElementById('imagePreview');
    const uploadContent = uploadArea.querySelector('.upload-content');
    const previewContainer = uploadArea.querySelector('.preview-container');
    const removeBtn = document.getElementById('removeImage');

    // Click to upload
    uploadArea.addEventListener('click', () => {
        if (!previewContainer.classList.contains('d-none')) return;
        fileInput.click();
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });

    // File selection
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove image
    removeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        fileInput.value = '';
        uploadContent.classList.remove('d-none');
        previewContainer.classList.add('d-none');
    });

    function handleFileSelect(file) {
        if (!file.type.startsWith('image/')) {
            alert('Veuillez sélectionner un fichier image');
            return;
        }

        if (file.size > 10 * 1024 * 1024) {
            alert('Le fichier est trop volumineux (max 10MB)');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            imagePreview.src = e.target.result;
            uploadContent.classList.add('d-none');
            previewContainer.classList.remove('d-none');
        };
        reader.readAsDataURL(file);
    }

    // Form submission
    document.getElementById('palmistryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const button = document.getElementById('analyzeBtn');
        const originalText = button.innerHTML;
        
        button.classList.add('btn-loading');
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyse en cours...';
        button.disabled = true;
        
        // Submit the form
        setTimeout(() => {
            this.submit();
        }, 1000);
    });
});

function showReadingDetails(readingId) {
    const modalBody = document.getElementById('readingModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement de la lecture...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('readingModal'));
    modal.show();

    // Load reading details via AJAX
    fetch(`/api/palm-reading/${readingId}`)
        .then(response => response.json())
        .then(data => {
            modalBody.innerHTML = `
                <div class="reading-details">
                    <div class="text-center mb-4">
                        <img src="/static/uploads/palms/${data.image_filename}" alt="Paume analysée"
                             class="img-fluid rounded border border-gold" style="max-height: 200px;">
                        <div class="mt-2">
                            <span class="badge bg-gold text-dark">${data.client_name}</span>
                            <span class="badge bg-info text-dark ms-2">${data.hand_type}</span>
                        </div>
                    </div>

                    ${data.question ? `
                    <h6 class="text-purple mb-3">Question Posée</h6>
                    <div class="question-content p-3 bg-dark-tertiary rounded mb-4">
                        <p class="text-light">${data.question}</p>
                    </div>
                    ` : ''}

                    <h6 class="text-gold mb-3">Analyse des Lignes</h6>
                    <div class="analysis-content p-3 bg-dark-tertiary rounded mb-4">
                        <div class="text-light">${data.palm_analysis}</div>
                    </div>

                    <h6 class="text-gold mb-3">Interprétation Spirituelle</h6>
                    <div class="interpretation-content p-3 bg-dark-secondary rounded mb-4">
                        <div class="text-light">${data.interpretation}</div>
                    </div>

                    ${data.spiritual_advice ? `
                    <h6 class="text-purple mb-3">Conseils Spirituels</h6>
                    <div class="advice-content p-3 bg-dark-tertiary rounded">
                        <div class="text-light">${data.spiritual_advice}</div>
                    </div>
                    ` : ''}
                </div>
            `;
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>Erreur lors du chargement des détails</p>
                </div>
            `;
        });
}

function deleteReading(readingId, clientName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la lecture de ${clientName} ?`)) {
        fetch(`/api/palm-reading/${readingId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                // Reload the page to update the list
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            alert('Erreur lors de la suppression');
        });
    }
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Consultation Runique - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-mountain"></i> Votre Consultation Runique
                </h1>
                <p class="lead text-muted">
                    Les runes ont parlé - Découvrez leur message ancestral
                </p>
                <div class="nordic-decoration">
                    <span class="rune-symbol">{{ rune_symbols }}</span>
                </div>
            </div>

            <!-- Client Info -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-purple mb-0">
                        <i class="fas fa-user-shield"></i> Consultation pour {{ reading.client_name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item p-3 bg-dark-secondary rounded">
                                <h6 class="text-gold">Type de Tirage</h6>
                                <p class="text-light mb-0">
                                    {% if reading.reading_type == 'single' %}
                                        <i class="fas fa-circle"></i> Rune Unique - Guidance Immédiate
                                    {% elif reading.reading_type == 'three_rune' %}
                                        <i class="fas fa-circle"></i> Trois Nornes - Passé, Présent, Futur
                                    {% elif reading.reading_type == 'five_rune' %}
                                        <i class="fas fa-plus"></i> Croix d'Odin - Situation Complexe
                                    {% elif reading.reading_type == 'nine_rune' %}
                                        <i class="fas fa-tree"></i> Arbre Yggdrasil - Destinée Complète
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item p-3 bg-dark-secondary rounded">
                                <h6 class="text-gold">Date de Consultation</h6>
                                <p class="text-light mb-0">
                                    <i class="fas fa-calendar"></i> {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    {% if reading.question %}
                    <div class="mt-3 p-3 bg-dark-tertiary rounded">
                        <h6 class="text-purple">Question aux Nornes</h6>
                        <p class="text-light mb-0">{{ reading.question }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Runes Display -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-fire"></i> Les Runes Sacrées
                    </h5>
                </div>
                <div class="card-body">
                    <div class="runes-display-container">
                        {% set runes = reading.runes_drawn | from_json %}
                        <div class="row justify-content-center g-4">
                            {% for rune in runes %}
                            <div class="col-auto">
                                <div class="rune-card text-center">
                                    <div class="rune-stone">
                                        <div class="rune-symbol-large">{{ rune.symbol }}</div>
                                        <div class="rune-name">{{ rune.name }}</div>
                                    </div>
                                    <div class="rune-position mt-2">
                                        <span class="badge bg-gold text-dark">{{ rune.position }}</span>
                                    </div>
                                    <div class="rune-meaning mt-2">
                                        <small class="text-muted">{{ rune.meaning }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rune Analysis -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-search"></i> Analyse des Runes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="analysis-content">
                        <div class="analysis-text p-4 rounded">
                            {{ reading.rune_analysis|safe }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spiritual Interpretation -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-eye"></i> Interprétation Spirituelle
                    </h5>
                </div>
                <div class="card-body">
                    <div class="interpretation-content">
                        <div class="interpretation-text p-4 rounded">
                            {{ reading.interpretation|safe }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Völva Guidance -->
            {% if reading.volva_guidance %}
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-purple mb-0">
                        <i class="fas fa-crown"></i> Guidance de la Völva
                    </h5>
                </div>
                <div class="card-body">
                    <div class="volva-guidance-content">
                        <div class="guidance-text p-4 rounded">
                            {{ reading.volva_guidance|safe }}
                        </div>
                        
                        <div class="mt-4 p-3 bg-dark-secondary rounded">
                            <h6 class="text-warning">
                                <i class="fas fa-hammer"></i> Sagesse Nordique
                            </h6>
                            <ul class="text-muted mb-0">
                                <li>Méditez sur le message des runes chaque matin</li>
                                <li>Portez attention aux signes dans votre quotidien</li>
                                <li>Honorez les cycles naturels et les saisons</li>
                                <li>Cultivez votre connexion avec les éléments</li>
                                <li>Faites confiance à votre sagesse intérieure</li>
                                <li>Respectez les enseignements des ancêtres</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Actions -->
            <div class="text-center">
                <a href="{{ url_for('download_rune_pdf', reading_id=reading.id) }}" class="btn btn-gold me-3">
                    <i class="fas fa-download"></i> Télécharger PDF
                </a>
                <a href="{{ url_for('rune_reading') }}" class="btn btn-purple me-3">
                    <i class="fas fa-plus"></i> Nouvelle Consultation
                </a>
                <a href="{{ url_for('esoteric_tools') }}" class="btn btn-outline-gold">
                    <i class="fas fa-magic"></i> Autres Outils
                </a>
            </div>

            <!-- Date -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-mountain"></i> 
                    Consultation effectuée le {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.nordic-decoration {
    margin: 20px 0;
}

.rune-symbol {
    font-size: 3rem;
    color: var(--gold);
    text-shadow: 0 0 15px rgba(212, 175, 55, 0.7);
    letter-spacing: 8px;
}

.info-item {
    border-left: 4px solid var(--gold);
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.runes-display-container {
    background: linear-gradient(135deg, rgba(44, 90, 160, 0.1), rgba(75, 0, 130, 0.1));
    border-radius: 15px;
    padding: 30px;
    border: 2px solid var(--gold);
}

.rune-card {
    transition: all 0.3s ease;
}

.rune-card:hover {
    transform: translateY(-5px);
}

.rune-stone {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid var(--gold);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.rune-stone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1), transparent);
}

.rune-symbol-large {
    font-size: 3rem;
    color: var(--gold);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.rune-name {
    font-weight: bold;
    color: var(--gold);
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.rune-position {
    margin-top: 10px;
}

.rune-meaning {
    font-style: italic;
    line-height: 1.4;
}

.analysis-text {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(75, 0, 130, 0.1));
    border-left: 4px solid var(--gold);
    position: relative;
}

.analysis-text::before {
    content: '🔥';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.interpretation-text {
    background: linear-gradient(135deg, rgba(44, 90, 160, 0.1), rgba(75, 0, 130, 0.1));
    border-left: 4px solid var(--purple);
    position: relative;
}

.interpretation-text::before {
    content: '👁️';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.guidance-text {
    background: linear-gradient(135deg, rgba(128, 0, 128, 0.1), rgba(75, 0, 130, 0.1));
    border-left: 4px solid var(--purple);
    position: relative;
}

.guidance-text::before {
    content: '👑';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.volva-guidance-content ul {
    list-style-type: none;
    padding-left: 0;
}

.volva-guidance-content li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.volva-guidance-content li:last-child {
    border-bottom: none;
}

.volva-guidance-content li::before {
    content: '⚡';
    margin-right: 10px;
    color: var(--gold);
}

@media (max-width: 768px) {
    .rune-symbol {
        font-size: 2rem;
        letter-spacing: 4px;
    }
    
    .rune-symbol-large {
        font-size: 2rem;
    }
    
    .runes-display-container {
        padding: 20px;
    }
}
</style>
{% endblock %}

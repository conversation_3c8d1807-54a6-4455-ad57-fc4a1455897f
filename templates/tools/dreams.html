{% extends "base.html" %}

{% block title %}Interprétation des Rêves - Temple Du Voile{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="mystical-font text-purple mb-3">
                    <i class="fas fa-moon"></i> Interprétation des Rêves
                </h1>
                <p class="lead text-muted">
                    Découvrez les messages cachés de votre subconscient
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Dream Interpretation Form -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-purple mb-0">
                        <i class="fas fa-feather-alt"></i> Racontez Votre Rêve
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST">
                        <div class="mb-4">
                            <label for="dream_description" class="form-label text-purple">
                                <i class="fas fa-scroll"></i> Description du Rêve *
                            </label>
                            <textarea class="form-control form-control-dark" 
                                      id="dream_description" name="dream_description" 
                                      rows="6" required
                                      placeholder="Décrivez votre rêve en détail... Plus vous fournirez d'informations, plus l'interprétation sera précise."></textarea>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> 
                                Incluez les lieux, personnes, objets, actions et sensations que vous avez ressentis.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="emotions" class="form-label text-purple">
                                        <i class="fas fa-heart"></i> Émotions Ressenties
                                    </label>
                                    <input type="text" class="form-control form-control-dark" 
                                           id="emotions" name="emotions"
                                           placeholder="Peur, joie, tristesse, confusion...">
                                    <div class="form-text text-muted">
                                        Les émotions sont clés pour l'interprétation
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="symbols" class="form-label text-purple">
                                        <i class="fas fa-eye"></i> Symboles Marquants
                                    </label>
                                    <input type="text" class="form-control form-control-dark" 
                                           id="symbols" name="symbols"
                                           placeholder="Animaux, couleurs, objets spéciaux...">
                                    <div class="form-text text-muted">
                                        Éléments qui vous ont particulièrement marqué
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subscription Level Info -->
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-crown fa-2x text-gold me-3"></i>
                                <div>
                                    <h6 class="alert-heading">Votre Niveau : {{ current_user.subscription_level.title() }}</h6>
                                    {% if current_user.subscription_level == 'standard' %}
                                    <p class="mb-0">Interprétation basique automatisée</p>
                                    {% elif current_user.subscription_level == 'premium' %}
                                    <p class="mb-0">Interprétation avancée avec analyse symbolique</p>
                                    {% else %}
                                    <p class="mb-0">Interprétation experte + option de consultation humaine</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-purple btn-lg">
                                <i class="fas fa-magic"></i> Interpréter Mon Rêve
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Dream History -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h4 class="mystical-font text-purple mb-0">
                        <i class="fas fa-history"></i> Mes Rêves Précédents
                    </h4>
                </div>
                <div class="card-body">
                    {% if dreams %}
                        {% for dream in dreams %}
                        <div class="dream-item mb-3 p-3 border border-secondary rounded position-relative" id="dream-{{ dream.id }}">
                            <!-- Delete button positioned better -->
                            <button class="btn btn-sm btn-danger delete-btn"
                                    onclick="deleteDream({{ dream.id }})"
                                    title="Supprimer cette interprétation">
                                <i class="fas fa-times"></i>
                            </button>

                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <small class="text-light">
                                    <i class="fas fa-calendar text-purple"></i>
                                    {{ dream.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                </small>
                            </div>

                            <p class="small text-white mb-2">
                                {{ dream.dream_description[:80] }}{% if dream.dream_description|length > 80 %}...{% endif %}
                            </p>

                            {% if dream.emotions %}
                            <div class="mb-2">
                                <span class="badge bg-purple small">
                                    <i class="fas fa-heart"></i> {{ dream.emotions }}
                                </span>
                            </div>
                            {% endif %}

                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-purple btn-sm"
                                        onclick="viewDreamInterpretation({{ dream.id }})">
                                    <i class="fas fa-eye"></i> Voir l'interprétation
                                </button>
                                <a href="{{ url_for('download_dream_pdf', dream_id=dream.id) }}" class="btn btn-outline-gold btn-sm">
                                    <i class="fas fa-download"></i> PDF
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% if dreams|length >= 10 %}
                        <div class="text-center">
                            <a href="#all-dreams" class="btn btn-outline-purple btn-sm">
                                <i class="fas fa-list"></i> Voir Tous Mes Rêves
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-moon fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun rêve interprété pour le moment</p>
                            <p class="small text-muted">
                                Commencez par décrire votre premier rêve ci-contre
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Dream Tips -->
            <div class="card card-dark mt-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-lightbulb"></i> Conseils pour Mieux Rêver
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-book text-purple me-2"></i>
                            <strong>Tenez un carnet de rêves</strong><br>
                            <small class="text-muted">Notez vos rêves dès le réveil</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-bed text-purple me-2"></i>
                            <strong>Routine de sommeil</strong><br>
                            <small class="text-muted">Couchez-vous et levez-vous à heures fixes</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-leaf text-purple me-2"></i>
                            <strong>Tisanes relaxantes</strong><br>
                            <small class="text-muted">Camomille, verveine avant le coucher</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-meditation text-purple me-2"></i>
                            <strong>Méditation</strong><br>
                            <small class="text-muted">Pratiquez la relaxation avant de dormir</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dream Details Modal -->
<div class="modal fade" id="dreamModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-secondary border-purple">
            <div class="modal-header bg-dark-tertiary border-purple">
                <h5 class="modal-title mystical-font text-purple">
                    <i class="fas fa-moon"></i> Détails du Rêve
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="dreamModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.btn-purple {
    background-color: var(--purple-light);
    border-color: var(--purple-light);
    color: white;
}

.btn-purple:hover {
    background-color: var(--purple-deep);
    border-color: var(--purple-deep);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(123, 31, 162, 0.4);
}

.dream-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
    border: 1px solid rgba(123, 31, 162, 0.3) !important;
}

.dream-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(123, 31, 162, 0.2);
    border-color: var(--purple) !important;
}

/* Delete button styling */
.delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(220, 53, 69, 0.8);
    border: 1px solid #dc3545;
    color: white;
    font-size: 12px;
    transition: all 0.3s ease;
    z-index: 10;
}

.delete-btn:hover {
    background-color: #dc3545;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
}

.delete-btn:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* Improve text contrast */
.dream-item .text-white {
    color: #ffffff !important;
}

.dream-item .text-light {
    color: #f8f9fa !important;
}

.dream-item .text-purple {
    color: var(--purple) !important;
    font-weight: 600;
}

/* Badge styling */
.dream-item .badge {
    font-weight: 600;
    padding: 4px 8px;
    margin-right: 35px; /* Space for delete button */
}

/* Button improvements */
.dream-item .btn-outline-purple {
    border-color: var(--purple);
    color: var(--purple);
}

.dream-item .btn-outline-purple:hover {
    background-color: var(--purple);
    color: white;
    transform: translateY(-1px);
}

.dream-item .btn-outline-gold {
    border-color: var(--gold);
    color: var(--gold);
}

.dream-item .btn-outline-gold:hover {
    background-color: var(--gold);
    color: var(--dark-bg);
    transform: translateY(-1px);
}

.border-purple {
    border-color: var(--purple-light) !important;
}

.form-control-dark:focus {
    border-color: var(--purple-light);
    box-shadow: 0 0 0 0.2rem rgba(123, 31, 162, 0.25);
}
</style>

<script>
function viewDreamInterpretation(dreamId) {
    // Redirect to the dream result page
    window.location.href = `/tools/dreams/result/${dreamId}`;
}

function deleteDream(dreamId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette interprétation de rêve ?')) {
        fetch(`/api/dreams/${dreamId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                // Remove the dream item from the DOM
                const dreamElement = document.getElementById(`dream-${dreamId}`);
                if (dreamElement) {
                    dreamElement.style.transition = 'all 0.3s ease';
                    dreamElement.style.opacity = '0';
                    dreamElement.style.transform = 'translateX(-100%)';

                    setTimeout(() => {
                        dreamElement.remove();

                        // Check if there are no more dreams
                        const remainingDreams = document.querySelectorAll('.dream-item');
                        if (remainingDreams.length === 0) {
                            location.reload(); // Reload to show "no dreams" message
                        }
                    }, 300);
                }

                // Show success message
                showNotification('Interprétation supprimée avec succès', 'success');
            } else {
                showNotification('Erreur lors de la suppression', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur lors de la suppression', 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %}

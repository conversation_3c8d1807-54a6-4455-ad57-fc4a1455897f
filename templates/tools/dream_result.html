{% extends "base.html" %}

{% block title %}Interprétation de Rêve - Temple Du Voile{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-moon"></i> Interprétation de votre Rêve
                </h1>
                <p class="lead text-muted">
                    Les messages de votre inconscient révélés par l'analyse jungienne
                </p>
                <div class="dream-decoration">
                    <span class="dream-symbol">🌙</span>
                </div>
            </div>

            <!-- Dream Details -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-purple mb-0">
                        <i class="fas fa-cloud"></i> Description du Rêve
                    </h5>
                </div>
                <div class="card-body">
                    <div class="dream-description p-3 bg-dark-secondary rounded">
                        <p class="text-light mb-0">{{ dream.dream_description }}</p>
                    </div>

                    <div class="row mt-4">
                        {% if dream.emotions %}
                        <div class="col-md-6">
                            <div class="emotion-box p-3 bg-dark-tertiary rounded">
                                <h6 class="text-gold">
                                    <i class="fas fa-heart"></i> Émotions Ressenties
                                </h6>
                                <p class="text-muted mb-0">{{ dream.emotions }}</p>
                            </div>
                        </div>
                        {% endif %}

                        {% if dream.symbols %}
                        <div class="col-md-6">
                            <div class="symbols-box p-3 bg-dark-tertiary rounded">
                                <h6 class="text-purple">
                                    <i class="fas fa-star"></i> Symboles Marquants
                                </h6>
                                <p class="text-muted mb-0">{{ dream.symbols }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- AI Interpretation -->
            <div class="card card-dark mb-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-eye"></i> Analyse Experte
                    </h5>
                </div>
                <div class="card-body">
                    <div class="interpretation-content">
                        <div class="interpretation-text p-4 rounded">
                            {{ dream.interpretation|safe|nl2br }}
                        </div>

                        <div class="mt-4 p-3 bg-dark-secondary rounded">
                            <h6 class="text-warning">
                                <i class="fas fa-lightbulb"></i> Conseils pour l'Intégration
                            </h6>
                            <ul class="text-muted mb-0">
                                <li>Tenez un journal de vos rêves pour identifier les patterns</li>
                                <li>Méditez sur les symboles qui vous marquent le plus</li>
                                <li>Explorez les émotions ressenties dans votre vie éveillée</li>
                                <li>Pratiquez la visualisation créatrice avant le sommeil</li>
                                <li>Partagez vos insights avec un proche de confiance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="text-center">
                <a href="{{ url_for('download_dream_pdf', dream_id=dream.id) }}" class="btn btn-gold me-3">
                    <i class="fas fa-download"></i> Télécharger PDF
                </a>
                <a href="{{ url_for('dream_interpretation') }}" class="btn btn-purple me-3">
                    <i class="fas fa-plus"></i> Nouvelle Interprétation
                </a>
                <a href="{{ url_for('esoteric_tools') }}" class="btn btn-outline-gold">
                    <i class="fas fa-magic"></i> Autres Outils
                </a>
            </div>

            <!-- Date -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-moon"></i>
                    Analyse effectuée le {{ dream.created_at.strftime('%d/%m/%Y à %H:%M') }}
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.dream-decoration {
    margin: 20px 0;
}

.dream-symbol {
    font-size: 4rem;
    color: var(--purple);
    text-shadow: 0 0 20px rgba(123, 31, 162, 0.8);
    display: inline-block;
    padding: 20px;
    border: 3px solid var(--purple);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(123, 31, 162, 0.1), transparent);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(123, 31, 162, 0.5);
        transform: scale(1);
    }
    to {
        box-shadow: 0 0 30px rgba(123, 31, 162, 0.8);
        transform: scale(1.05);
    }
}

.dream-description {
    border-left: 4px solid var(--purple);
    position: relative;
}

.dream-description::before {
    content: '💭';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.emotion-box {
    border-left: 4px solid var(--gold);
    transition: all 0.3s ease;
}

.emotion-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.symbols-box {
    border-left: 4px solid var(--purple);
    transition: all 0.3s ease;
}

.symbols-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(123, 31, 162, 0.2);
}

.interpretation-text {
    background: linear-gradient(135deg, rgba(123, 31, 162, 0.1), rgba(75, 0, 130, 0.1));
    border-left: 4px solid var(--gold);
    position: relative;
    line-height: 1.8;
}

.interpretation-text::before {
    content: '🔮';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 2rem;
    opacity: 0.3;
}

.interpretation-content ul {
    list-style-type: none;
    padding-left: 0;
}

.interpretation-content li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(123, 31, 162, 0.2);
}

.interpretation-content li:last-child {
    border-bottom: none;
}

.interpretation-content li::before {
    content: '✨';
    margin-right: 10px;
    color: var(--gold);
}

@media (max-width: 768px) {
    .dream-symbol {
        font-size: 3rem;
        padding: 15px;
    }

    .row.mt-4 .col-md-6 {
        margin-bottom: 15px;
    }
}
</style>
{% endblock %}

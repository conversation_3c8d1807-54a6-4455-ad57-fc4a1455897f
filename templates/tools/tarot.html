{% extends "base.html" %}

{% block title %}Lectures de Tarot - Temple Du Voile{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-magic"></i> Lectures de Tarot
                </h1>
                <p class="lead text-muted">
                    Laissez les cartes révéler les secrets de votre destinée
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Tarot Reading Form -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-cards-blank"></i> Choisissez Votre Tirage
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="tarotForm">
                        <div class="mb-4">
                            <label class="form-label text-gold mb-3">
                                <i class="fas fa-layer-group"></i> Type de Tirage
                            </label>
                            
                            <div class="row g-3">
                                <!-- Single Card -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="single" value="single" 
                                                   {% if current_user.subscription_level in ['standard', 'premium', 'gold'] %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="single">
                                                <i class="fas fa-square fa-2x mb-2"></i>
                                                <h6 class="mystical-font">Tirage Simple</h6>
                                                <small class="text-muted">1 carte</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level in ['standard', 'premium', 'gold'] %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Standard+</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Three Cards -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="three_card" value="three_card"
                                                   {% if current_user.subscription_level in ['premium', 'gold'] %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="three_card">
                                                <div class="mb-2">
                                                    <i class="fas fa-square me-1"></i>
                                                    <i class="fas fa-square me-1"></i>
                                                    <i class="fas fa-square"></i>
                                                </div>
                                                <h6 class="mystical-font">Tirage 3 Cartes</h6>
                                                <small class="text-muted">Passé, Présent, Futur</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level in ['premium', 'gold'] %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Premium+</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Celtic Cross -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="celtic_cross" value="celtic_cross"
                                                   {% if current_user.subscription_level == 'gold' %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="celtic_cross">
                                                <i class="fas fa-plus fa-2x mb-2"></i>
                                                <h6 class="mystical-font">Croix Celtique</h6>
                                                <small class="text-muted">10 cartes</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level == 'gold' %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Gold</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Question -->
                        <div class="mb-4">
                            <label for="question" class="form-label text-gold">
                                <i class="fas fa-question-circle"></i> Votre Question (Optionnel)
                            </label>
                            <textarea class="form-control form-control-dark" 
                                      id="question" name="question" 
                                      rows="3"
                                      placeholder="Posez une question précise aux cartes ou laissez vide pour une lecture générale..."></textarea>
                            <div class="form-text text-muted">
                                <i class="fas fa-lightbulb"></i> 
                                Plus votre question est précise, plus l'interprétation sera pertinente.
                            </div>
                        </div>

                        <!-- Subscription Info -->
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-crown fa-2x text-gold me-3"></i>
                                <div>
                                    <h6 class="alert-heading">Votre Niveau : {{ current_user.subscription_level.title() }}</h6>
                                    {% if current_user.subscription_level == 'standard' %}
                                    <p class="mb-0">Tirage simple disponible. Passez à Premium pour plus d'options.</p>
                                    {% elif current_user.subscription_level == 'premium' %}
                                    <p class="mb-0">Tirages simple et 3 cartes disponibles. Passez à Gold pour la Croix Celtique.</p>
                                    {% else %}
                                    <p class="mb-0">Tous les types de tirages disponibles !</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="button" class="btn btn-gold btn-lg" id="startReadingBtn">
                                <i class="fas fa-magic"></i> Commencer le Tirage
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Card Selection Area (Hidden initially) -->
        <div class="col-12" id="cardSelectionArea" style="display: none;">
            <div class="card card-dark mt-4">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0 text-center">
                        <i class="fas fa-hand-sparkles"></i> Choisissez vos Cartes
                    </h3>
                    <p class="text-center text-muted mb-0 mt-2" id="selectionInstruction">
                        Concentrez-vous sur votre question et sélectionnez 1 carte
                    </p>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="selected-cards-counter">
                            <span class="badge bg-gold text-dark fs-6">
                                <span id="selectedCount">0</span> / <span id="totalCards">1</span> cartes sélectionnées
                            </span>
                        </div>
                    </div>

                    <!-- Card Grid -->
                    <div class="tarot-deck" id="tarotDeck">
                        <!-- Cards will be generated by JavaScript -->
                    </div>

                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-gold btn-lg" id="interpretCardsBtn" style="display: none;">
                            <i class="fas fa-eye"></i> Interpréter les Cartes
                        </button>
                        <button type="button" class="btn btn-outline-gold" id="resetSelectionBtn">
                            <i class="fas fa-redo"></i> Recommencer
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reading History -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h4 class="mystical-font text-gold mb-0">
                        <i class="fas fa-history"></i> Mes Tirages Précédents
                    </h4>
                </div>
                <div class="card-body">
                    {% if readings %}
                        {% for reading in readings %}
                        <div class="reading-item mb-3 p-3 border border-secondary rounded">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> 
                                    {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                </small>
                                <span class="badge bg-gold text-dark small">
                                    {{ reading.reading_type.replace('_', ' ').title() }}
                                </span>
                            </div>
                            
                            <p class="small text-light mb-2">
                                {{ reading.interpretation[:60] }}{% if reading.interpretation|length > 60 %}...{% endif %}
                            </p>
                            
                            <button class="btn btn-outline-gold btn-sm" 
                                    onclick="showReadingDetails({{ reading.id }})">
                                <i class="fas fa-eye"></i> Voir le Tirage
                            </button>
                        </div>
                        {% endfor %}
                        
                        {% if readings|length >= 10 %}
                        <div class="text-center">
                            <a href="#all-readings" class="btn btn-outline-gold btn-sm">
                                <i class="fas fa-list"></i> Voir Tous Mes Tirages
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-magic fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun tirage effectué pour le moment</p>
                            <p class="small text-muted">
                                Commencez par tirer vos premières cartes
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Tarot Guide -->
            <div class="card card-dark mt-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-book-open"></i> Guide du Tarot
                    </h5>
                </div>
                <div class="card-body">
                    <div class="tarot-tips">
                        <h6 class="text-gold mb-3">Conseils pour un Bon Tirage</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-heart text-gold me-2"></i>
                                <small>Concentrez-vous sur votre question</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-peace text-gold me-2"></i>
                                <small>Restez calme et ouvert</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-gold me-2"></i>
                                <small>Choisissez un moment tranquille</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-moon text-gold me-2"></i>
                                <small>Faites confiance à votre intuition</small>
                            </li>
                        </ul>
                        
                        <hr class="border-secondary my-3">
                        
                        <h6 class="text-gold mb-3">Types de Questions</h6>
                        <div class="question-examples">
                            <small class="text-muted d-block mb-1">
                                <strong>Amour :</strong> "Que dois-je savoir sur ma relation ?"
                            </small>
                            <small class="text-muted d-block mb-1">
                                <strong>Carrière :</strong> "Quelle direction prendre professionnellement ?"
                            </small>
                            <small class="text-muted d-block mb-1">
                                <strong>Spirituel :</strong> "Quel est mon chemin d'évolution ?"
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="readingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-secondary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title mystical-font text-gold">
                    <i class="fas fa-magic"></i> Détails du Tirage
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="readingModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.tarot-option {
    transition: all 0.3s ease;
}

.tarot-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.tarot-option input[type="radio"]:checked + label {
    background-color: var(--gold);
    color: var(--dark-bg);
    border-color: var(--gold);
}

.tarot-option input[type="radio"]:disabled + label {
    opacity: 0.6;
    cursor: not-allowed;
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.question-examples small {
    line-height: 1.4;
}

.border-gold {
    border-color: var(--gold) !important;
}

/* Tarot Deck Styles */
.tarot-deck {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    max-height: 500px;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 15px;
    border: 2px solid var(--gold);
}

.tarot-card {
    width: 120px;
    height: 180px;
    border: 3px solid var(--gold);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: #000;
}

.tarot-card .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.tarot-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
    border-color: #ffd700;
}

.tarot-card.preview {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.tarot-card.selected {
    transform: translateY(-12px) scale(1.1);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.6);
    border-color: #ffd700;
    border-width: 4px;
    animation: cardGlow 2s infinite alternate;
}

.tarot-card.selected::after {
    content: '✓';
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--gold);
    color: var(--dark-bg);
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

@keyframes cardGlow {
    0% { box-shadow: 0 15px 30px rgba(212, 175, 55, 0.5); }
    100% { box-shadow: 0 15px 30px rgba(212, 175, 55, 0.8); }
}

.selected-cards-counter {
    margin-bottom: 20px;
}

.btn-loading {
    position: relative;
    overflow: hidden;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tarot-deck {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 8px;
        padding: 15px;
    }

    .tarot-card-back {
        width: 60px;
        height: 90px;
    }

    .tarot-card-back::before {
        font-size: 1.5rem;
    }
}
</style>

<script>
// Tarot cards data (78 cards) - Loaded from server
const tarotCards = [
    // Major Arcana
    {id: "00", name: "Le Mat", type: "major", meaning: "Nouveau départ, innocence, spontanéité", image: "00-le-mat.jpg"},
    {id: "01", name: "Le Bateleur", type: "major", meaning: "Créativité, habileté, volonté", image: "01-le-bateleur.jpg"},
    {id: "02", name: "La Papesse", type: "major", meaning: "Intuition, mystère, sagesse intérieure", image: "02-la-papesse.jpg"},
    {id: "03", name: "L'Impératrice", type: "major", meaning: "Féminité, créativité, abondance", image: "03-limpératrice.jpg"},
    {id: "04", name: "L'Empereur", type: "major", meaning: "Autorité, structure, contrôle", image: "04-lempereur.jpg"},
    {id: "05", name: "Le Pape", type: "major", meaning: "Tradition, conformité, moralité", image: "05-le-pape.jpg"},
    {id: "06", name: "L'Amoureux", type: "major", meaning: "Amour, choix, union", image: "06-lamoureux.jpg"},
    {id: "07", name: "Le Chariot", type: "major", meaning: "Volonté, détermination, victoire", image: "07-le-chariot.jpg"},
    {id: "08", name: "La Justice", type: "major", meaning: "Équité, vérité, cause et effet", image: "08-la-justice.jpg"},
    {id: "09", name: "L'Hermite", type: "major", meaning: "Introspection, recherche, guidance intérieure", image: "09-lhermite.jpg"},
    {id: "10", name: "La Roue de Fortune", type: "major", meaning: "Changement, cycles, destinée", image: "10-la-roue-de-fortune.jpg"},
    {id: "11", name: "La Force", type: "major", meaning: "Force intérieure, courage, patience", image: "11-la-force.jpg"},
    {id: "12", name: "Le Pendu", type: "major", meaning: "Sacrifice, attente, nouvelle perspective", image: "12-le-pendu.jpg"},
    {id: "13", name: "L'Arcane sans Nom", type: "major", meaning: "Transformation, fin, renouveau", image: "13-larcane-sans-nom.jpg"},
    {id: "14", name: "Tempérance", type: "major", meaning: "Modération, patience, but", image: "14-tempérance.jpg"},
    {id: "15", name: "Le Diable", type: "major", meaning: "Bondage, addiction, sexualité", image: "15-le-diable.jpg"},
    {id: "16", name: "La Maison Dieu", type: "major", meaning: "Révélation soudaine, bouleversement", image: "16-la-maison-dieu.jpg"},
    {id: "17", name: "L'Étoile", type: "major", meaning: "Espoir, spiritualité, renouveau", image: "17-létoile.jpg"},
    {id: "18", name: "La Lune", type: "major", meaning: "Illusion, peur, subconscient", image: "18-la-lune.jpg"},
    {id: "19", name: "Le Soleil", type: "major", meaning: "Joie, succès, vitalité", image: "19-le-soleil.jpg"},
    {id: "20", name: "Le Jugement", type: "major", meaning: "Jugement, renaissance, pardon", image: "20-le-jugement.jpg"},
    {id: "21", name: "Le Monde", type: "major", meaning: "Accomplissement, voyage, succès", image: "21-le-monde.jpg"},

    // Coupes
    {id: "cups-01", name: "As de Coupe", type: "cups", meaning: "Nouveau départ émotionnel, amour", image: "cups-01-as-de-coupe.jpg"},
    {id: "cups-02", name: "Deux de Coupe", type: "cups", meaning: "Partenariat, union, connexion", image: "cups-02-deux-de-coupe.jpg"},
    {id: "cups-03", name: "Trois de Coupe", type: "cups", meaning: "Célébration, amitié, communauté", image: "cups-03-trois-de-coupe.jpg"},
    {id: "cups-04", name: "Quatre de Coupe", type: "cups", meaning: "Apathie, contemplation, réévaluation", image: "cups-04-quatre-de-coupe.jpg"},
    {id: "cups-05", name: "Cinq de Coupe", type: "cups", meaning: "Regret, perte, déception", image: "cups-05-cinq-de-coupe.jpg"},
    {id: "cups-06", name: "Six de Coupe", type: "cups", meaning: "Nostalgie, enfance, innocence", image: "cups-06-six-de-coupe.jpg"},
    {id: "cups-07", name: "Sept de Coupe", type: "cups", meaning: "Illusion, choix, rêverie", image: "cups-07-sept-de-coupe.jpg"},
    {id: "cups-08", name: "Huit de Coupe", type: "cups", meaning: "Abandon, recherche spirituelle", image: "cups-08-huit-de-coupe.jpg"},
    {id: "cups-09", name: "Neuf de Coupe", type: "cups", meaning: "Satisfaction, bonheur, accomplissement", image: "cups-09-neuf-de-coupe.jpg"},
    {id: "cups-10", name: "Dix de Coupe", type: "cups", meaning: "Bonheur familial, harmonie", image: "cups-10-dix-de-coupe.jpg"},
    {id: "cups-11", name: "Valet de Coupe", type: "cups", meaning: "Messager émotionnel, créativité", image: "cups-11-valet-de-coupe.jpg"},
    {id: "cups-12", name: "Cavalier de Coupe", type: "cups", meaning: "Romance, charme, invitation", image: "cups-12-cavalier-de-coupe.jpg"},
    {id: "cups-13", name: "Reine de Coupe", type: "cups", meaning: "Intuition, compassion, sécurité émotionnelle", image: "cups-13-reine-de-coupe.jpg"},
    {id: "cups-14", name: "Roi de Coupe", type: "cups", meaning: "Maîtrise émotionnelle, diplomatie", image: "cups-14-roi-de-coupe.jpg"}
];

let selectedCards = [];
let currentReadingType = '';
let maxCards = 1;

// Initialize the tarot system
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for reading type selection
    document.querySelectorAll('input[name="reading_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            document.querySelectorAll('.tarot-option').forEach(option => {
                option.classList.remove('selected');
            });

            if (this.checked) {
                this.closest('.tarot-option').classList.add('selected');
                currentReadingType = this.value;

                // Set max cards based on reading type
                switch(currentReadingType) {
                    case 'single':
                        maxCards = 1;
                        break;
                    case 'three_card':
                        maxCards = 3;
                        break;
                    case 'celtic_cross':
                        maxCards = 10;
                        break;
                }
            }
        });
    });

    // Start reading button
    document.getElementById('startReadingBtn').addEventListener('click', startReading);

    // Reset selection button
    document.getElementById('resetSelectionBtn').addEventListener('click', resetSelection);

    // Interpret cards button
    document.getElementById('interpretCardsBtn').addEventListener('click', interpretCards);
});

function startReading() {
    if (!currentReadingType) {
        alert('Veuillez sélectionner un type de tirage');
        return;
    }

    // Hide form and show card selection
    document.querySelector('.col-lg-8').style.display = 'none';
    document.getElementById('cardSelectionArea').style.display = 'block';

    // Update instruction text
    const instruction = document.getElementById('selectionInstruction');
    switch(currentReadingType) {
        case 'single':
            instruction.textContent = 'Concentrez-vous sur votre question et sélectionnez 1 carte';
            break;
        case 'three_card':
            instruction.textContent = 'Sélectionnez 3 cartes pour Passé, Présent, Futur';
            break;
        case 'celtic_cross':
            instruction.textContent = 'Sélectionnez 10 cartes pour la Croix Celtique';
            break;
    }

    // Update counter
    document.getElementById('totalCards').textContent = maxCards;

    // Generate card deck
    generateCardDeck();
}

function generateCardDeck() {
    const deck = document.getElementById('tarotDeck');
    deck.innerHTML = '';

    // Shuffle cards
    const shuffledCards = [...tarotCards].sort(() => Math.random() - 0.5);

    // Create card elements
    shuffledCards.forEach((card, index) => {
        const cardElement = document.createElement('div');
        cardElement.className = 'tarot-card';
        cardElement.dataset.cardId = card.id;
        cardElement.dataset.cardName = card.name;
        cardElement.dataset.cardMeaning = card.meaning;
        cardElement.dataset.cardImage = card.image;

        // Create card image
        const cardImg = document.createElement('img');
        cardImg.src = `/static/images/tarot/card-back.jpg`;
        cardImg.alt = 'Dos de carte';
        cardImg.className = 'card-image';

        // Add hover effect to show card preview
        cardElement.addEventListener('mouseenter', () => {
            if (!cardElement.classList.contains('selected')) {
                cardImg.src = `/static/images/tarot/${card.image}`;
                cardElement.classList.add('preview');
            }
        });

        cardElement.addEventListener('mouseleave', () => {
            if (!cardElement.classList.contains('selected')) {
                cardImg.src = `/static/images/tarot/card-back.jpg`;
                cardElement.classList.remove('preview');
            }
        });

        cardElement.addEventListener('click', () => selectCard(cardElement, card));

        cardElement.appendChild(cardImg);
        deck.appendChild(cardElement);
    });
}

function selectCard(cardElement, card) {
    if (selectedCards.length >= maxCards && !cardElement.classList.contains('selected')) {
        return;
    }

    const cardImg = cardElement.querySelector('.card-image');

    if (cardElement.classList.contains('selected')) {
        // Deselect card
        cardElement.classList.remove('selected');
        cardImg.src = `/static/images/tarot/card-back.jpg`;
        selectedCards = selectedCards.filter(c => c.id !== card.id);
    } else {
        // Select card
        cardElement.classList.add('selected');
        cardImg.src = `/static/images/tarot/${card.image}`;
        selectedCards.push(card);
    }

    // Update counter
    document.getElementById('selectedCount').textContent = selectedCards.length;

    // Show interpret button when enough cards are selected
    const interpretBtn = document.getElementById('interpretCardsBtn');
    if (selectedCards.length === maxCards) {
        interpretBtn.style.display = 'inline-block';
    } else {
        interpretBtn.style.display = 'none';
    }
}

function resetSelection() {
    selectedCards = [];
    document.getElementById('selectedCount').textContent = '0';
    document.getElementById('interpretCardsBtn').style.display = 'none';

    // Reset visual selection
    document.querySelectorAll('.tarot-card-back').forEach(card => {
        card.classList.remove('selected');
    });

    // Show form again
    document.querySelector('.col-lg-8').style.display = 'block';
    document.getElementById('cardSelectionArea').style.display = 'none';
}

function interpretCards() {
    if (selectedCards.length !== maxCards) {
        alert('Veuillez sélectionner toutes les cartes requises');
        return;
    }

    const question = document.getElementById('question').value || 'Lecture générale';

    // Show loading
    const interpretBtn = document.getElementById('interpretCardsBtn');
    interpretBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Interprétation en cours...';
    interpretBtn.disabled = true;

    // Prepare data for submission
    const formData = new FormData();
    formData.append('reading_type', currentReadingType);
    formData.append('question', question);
    formData.append('selected_cards', JSON.stringify(selectedCards));

    // Submit to server
    fetch('{{ url_for("tarot_reading") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.href = response.url;
        } else {
            throw new Error('Erreur lors de l\'interprétation');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue lors de l\'interprétation');
        interpretBtn.innerHTML = '<i class="fas fa-eye"></i> Interpréter les Cartes';
        interpretBtn.disabled = false;
    });
}

function showReadingDetails(readingId) {
    const modalBody = document.getElementById('readingModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement du tirage...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('readingModal'));
    modal.show();

    // Load reading details (you can implement this with AJAX)
    setTimeout(() => {
        modalBody.innerHTML = `
            <div class="reading-details">
                <div class="cards-display mb-4">
                    <div class="row justify-content-center">
                        <div class="col-auto">
                            <div class="tarot-card-display">
                                <div class="card bg-gold text-dark text-center p-3">
                                    <h6 class="mystical-font">Lecture Précédente</h6>
                                    <i class="fas fa-magic fa-2x"></i>
                                    <small>Détails du tirage</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h6 class="text-gold mb-3">Interprétation</h6>
                <div class="interpretation-content p-3 bg-dark-tertiary rounded">
                    <p class="text-light">Les détails de votre tirage précédent apparaîtraient ici...</p>
                </div>
            </div>
        `;
    }, 1000);
}
</script>
{% endblock %}

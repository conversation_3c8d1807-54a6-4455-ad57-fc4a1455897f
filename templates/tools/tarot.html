{% extends "base.html" %}

{% block title %}Lectures de Tarot - TataMystik{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="mystical-font text-gold mb-3">
                    <i class="fas fa-magic"></i> Lectures de Tarot
                </h1>
                <p class="lead text-muted">
                    Laissez les cartes révéler les secrets de votre destinée
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Tarot Reading Form -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h3 class="mystical-font text-gold mb-0">
                        <i class="fas fa-cards-blank"></i> Choisissez Votre Tirage
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="tarotForm">
                        <div class="mb-4">
                            <label class="form-label text-gold mb-3">
                                <i class="fas fa-layer-group"></i> Type de Tirage
                            </label>
                            
                            <div class="row g-3">
                                <!-- Single Card -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="single" value="single" 
                                                   {% if current_user.subscription_level in ['standard', 'premium', 'gold'] %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="single">
                                                <i class="fas fa-square fa-2x mb-2"></i>
                                                <h6 class="mystical-font">Tirage Simple</h6>
                                                <small class="text-muted">1 carte</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level in ['standard', 'premium', 'gold'] %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Standard+</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Three Cards -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="three_card" value="three_card"
                                                   {% if current_user.subscription_level in ['premium', 'gold'] %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="three_card">
                                                <div class="mb-2">
                                                    <i class="fas fa-square me-1"></i>
                                                    <i class="fas fa-square me-1"></i>
                                                    <i class="fas fa-square"></i>
                                                </div>
                                                <h6 class="mystical-font">Tirage 3 Cartes</h6>
                                                <small class="text-muted">Passé, Présent, Futur</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level in ['premium', 'gold'] %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Premium+</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Celtic Cross -->
                                <div class="col-md-4">
                                    <div class="card card-dark h-100 tarot-option">
                                        <div class="card-body text-center p-3">
                                            <input type="radio" class="btn-check" name="reading_type" 
                                                   id="celtic_cross" value="celtic_cross"
                                                   {% if current_user.subscription_level == 'gold' %}{% else %}disabled{% endif %}>
                                            <label class="btn btn-outline-gold w-100 h-100 d-flex flex-column justify-content-center" 
                                                   for="celtic_cross">
                                                <i class="fas fa-plus fa-2x mb-2"></i>
                                                <h6 class="mystical-font">Croix Celtique</h6>
                                                <small class="text-muted">10 cartes</small>
                                                <div class="mt-2">
                                                    {% if current_user.subscription_level == 'gold' %}
                                                    <span class="badge bg-success">Disponible</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">Gold</span>
                                                    {% endif %}
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Question -->
                        <div class="mb-4">
                            <label for="question" class="form-label text-gold">
                                <i class="fas fa-question-circle"></i> Votre Question (Optionnel)
                            </label>
                            <textarea class="form-control form-control-dark" 
                                      id="question" name="question" 
                                      rows="3"
                                      placeholder="Posez une question précise aux cartes ou laissez vide pour une lecture générale..."></textarea>
                            <div class="form-text text-muted">
                                <i class="fas fa-lightbulb"></i> 
                                Plus votre question est précise, plus l'interprétation sera pertinente.
                            </div>
                        </div>

                        <!-- Subscription Info -->
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-crown fa-2x text-gold me-3"></i>
                                <div>
                                    <h6 class="alert-heading">Votre Niveau : {{ current_user.subscription_level.title() }}</h6>
                                    {% if current_user.subscription_level == 'standard' %}
                                    <p class="mb-0">Tirage simple disponible. Passez à Premium pour plus d'options.</p>
                                    {% elif current_user.subscription_level == 'premium' %}
                                    <p class="mb-0">Tirages simple et 3 cartes disponibles. Passez à Gold pour la Croix Celtique.</p>
                                    {% else %}
                                    <p class="mb-0">Tous les types de tirages disponibles !</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg" id="drawCardsBtn">
                                <i class="fas fa-magic"></i> Tirer les Cartes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Reading History -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header bg-dark-tertiary">
                    <h4 class="mystical-font text-gold mb-0">
                        <i class="fas fa-history"></i> Mes Tirages Précédents
                    </h4>
                </div>
                <div class="card-body">
                    {% if readings %}
                        {% for reading in readings %}
                        <div class="reading-item mb-3 p-3 border border-secondary rounded">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> 
                                    {{ reading.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                </small>
                                <span class="badge bg-gold text-dark small">
                                    {{ reading.reading_type.replace('_', ' ').title() }}
                                </span>
                            </div>
                            
                            <p class="small text-light mb-2">
                                {{ reading.interpretation[:60] }}{% if reading.interpretation|length > 60 %}...{% endif %}
                            </p>
                            
                            <button class="btn btn-outline-gold btn-sm" 
                                    onclick="showReadingDetails({{ reading.id }})">
                                <i class="fas fa-eye"></i> Voir le Tirage
                            </button>
                        </div>
                        {% endfor %}
                        
                        {% if readings|length >= 10 %}
                        <div class="text-center">
                            <a href="#all-readings" class="btn btn-outline-gold btn-sm">
                                <i class="fas fa-list"></i> Voir Tous Mes Tirages
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-magic fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun tirage effectué pour le moment</p>
                            <p class="small text-muted">
                                Commencez par tirer vos premières cartes
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Tarot Guide -->
            <div class="card card-dark mt-4">
                <div class="card-header bg-dark-tertiary">
                    <h5 class="mystical-font text-gold mb-0">
                        <i class="fas fa-book-open"></i> Guide du Tarot
                    </h5>
                </div>
                <div class="card-body">
                    <div class="tarot-tips">
                        <h6 class="text-gold mb-3">Conseils pour un Bon Tirage</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-heart text-gold me-2"></i>
                                <small>Concentrez-vous sur votre question</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-peace text-gold me-2"></i>
                                <small>Restez calme et ouvert</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-gold me-2"></i>
                                <small>Choisissez un moment tranquille</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-moon text-gold me-2"></i>
                                <small>Faites confiance à votre intuition</small>
                            </li>
                        </ul>
                        
                        <hr class="border-secondary my-3">
                        
                        <h6 class="text-gold mb-3">Types de Questions</h6>
                        <div class="question-examples">
                            <small class="text-muted d-block mb-1">
                                <strong>Amour :</strong> "Que dois-je savoir sur ma relation ?"
                            </small>
                            <small class="text-muted d-block mb-1">
                                <strong>Carrière :</strong> "Quelle direction prendre professionnellement ?"
                            </small>
                            <small class="text-muted d-block mb-1">
                                <strong>Spirituel :</strong> "Quel est mon chemin d'évolution ?"
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reading Details Modal -->
<div class="modal fade" id="readingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-secondary border-gold">
            <div class="modal-header bg-dark-tertiary border-gold">
                <h5 class="modal-title mystical-font text-gold">
                    <i class="fas fa-magic"></i> Détails du Tirage
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="readingModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.tarot-option {
    transition: all 0.3s ease;
}

.tarot-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.tarot-option input[type="radio"]:checked + label {
    background-color: var(--gold);
    color: var(--dark-bg);
    border-color: var(--gold);
}

.tarot-option input[type="radio"]:disabled + label {
    opacity: 0.6;
    cursor: not-allowed;
}

.reading-item {
    background: linear-gradient(135deg, var(--dark-tertiary) 0%, var(--purple-deep) 100%);
    transition: all 0.3s ease;
}

.reading-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.2);
}

.question-examples small {
    line-height: 1.4;
}

.border-gold {
    border-color: var(--gold) !important;
}

#drawCardsBtn {
    position: relative;
    overflow: hidden;
}

#drawCardsBtn.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
</style>

<script>
function showReadingDetails(readingId) {
    const modalBody = document.getElementById('readingModalBody');
    modalBody.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x text-gold mb-3"></i>
            <p>Chargement du tirage...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('readingModal'));
    modal.show();
    
    // Simulate loading
    setTimeout(() => {
        modalBody.innerHTML = `
            <div class="reading-details">
                <div class="cards-display mb-4">
                    <div class="row justify-content-center">
                        <div class="col-auto">
                            <div class="tarot-card-display">
                                <div class="card bg-gold text-dark text-center p-3">
                                    <h6 class="mystical-font">Le Bateleur</h6>
                                    <i class="fas fa-magic fa-2x"></i>
                                    <small>Nouveau départ</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h6 class="text-gold mb-3">Interprétation</h6>
                <div class="interpretation-content p-3 bg-dark-tertiary rounded">
                    <p class="text-light">L'interprétation détaillée de votre tirage apparaîtrait ici...</p>
                </div>
            </div>
        `;
    }, 1000);
}

// Form submission with animation
document.getElementById('tarotForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const button = document.getElementById('drawCardsBtn');
    const originalText = button.innerHTML;
    
    button.classList.add('loading');
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mélange des cartes...';
    button.disabled = true;
    
    // Simulate card drawing
    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-magic"></i> Interprétation en cours...';
    }, 2000);
    
    setTimeout(() => {
        // Submit the form
        this.submit();
    }, 4000);
});

// Add visual feedback for radio selection
document.querySelectorAll('input[name="reading_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        document.querySelectorAll('.tarot-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        if (this.checked) {
            this.closest('.tarot-option').classList.add('selected');
        }
    });
});
</script>
{% endblock %}

#!/usr/bin/env python3
"""
Script pour initialiser la base de données avec la structure correcte
"""

from app import app, db
from models import User, PalmReading

def init_database():
    """Initialise la base de données"""
    try:
        with app.app_context():
            print("🔮 Initialisation de la base de données...")
            
            # Supprimer toutes les tables existantes
            db.drop_all()
            print("🗑️ Tables existantes supprimées")
            
            # Créer toutes les tables avec la nouvelle structure
            db.create_all()
            print("✅ Nouvelles tables créées")
            
            # Créer l'utilisateur admin
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin', 
                    email='<EMAIL>', 
                    subscription_level='gold', 
                    is_admin=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("👤 Utilisateur admin créé: admin/admin123")
            
            print("🎉 Base de données initialisée avec succès!")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False

if __name__ == "__main__":
    init_database()

#!/usr/bin/env python3
"""
Script pour créer des cartes de tarot stylisées avec PIL
"""

import os
from PIL import Image, ImageDraw, ImageFont
import random

# C<PERSON>er le dossier s'il n'existe pas
os.makedirs('static/images/tarot', exist_ok=True)

# Définition des cartes de tarot
tarot_cards = [
    # Arcanes Majeurs
    {"id": "00", "name": "Le Mat", "symbol": "🃏", "color": "#FFD700"},
    {"id": "01", "name": "Le Bateleur", "symbol": "🎩", "color": "#FF6B6B"},
    {"id": "02", "name": "<PERSON> Papesse", "symbol": "📚", "color": "#4ECDC4"},
    {"id": "03", "name": "L'Impératrice", "symbol": "👑", "color": "#45B7D1"},
    {"id": "04", "name": "<PERSON>'Empereur", "symbol": "⚔️", "color": "#96CEB4"},
    {"id": "05", "name": "Le Pape", "symbol": "⛪", "color": "#FFEAA7"},
    {"id": "06", "name": "L'Amoureux", "symbol": "💕", "color": "#FD79A8"},
    {"id": "07", "name": "Le Chariot", "symbol": "🏇", "color": "#6C5CE7"},
    {"id": "08", "name": "La Justice", "symbol": "⚖️", "color": "#A29BFE"},
    {"id": "09", "name": "L'Hermite", "symbol": "🕯️", "color": "#FD79A8"},
    {"id": "10", "name": "La Roue de Fortune", "symbol": "🎡", "color": "#FDCB6E"},
    {"id": "11", "name": "La Force", "symbol": "🦁", "color": "#E17055"},
    {"id": "12", "name": "Le Pendu", "symbol": "🙃", "color": "#00B894"},
    {"id": "13", "name": "L'Arcane sans Nom", "symbol": "💀", "color": "#2D3436"},
    {"id": "14", "name": "Tempérance", "symbol": "🏺", "color": "#74B9FF"},
    {"id": "15", "name": "Le Diable", "symbol": "😈", "color": "#E84393"},
    {"id": "16", "name": "La Maison Dieu", "symbol": "⚡", "color": "#FDCB6E"},
    {"id": "17", "name": "L'Étoile", "symbol": "⭐", "color": "#81ECEC"},
    {"id": "18", "name": "La Lune", "symbol": "🌙", "color": "#A29BFE"},
    {"id": "19", "name": "Le Soleil", "symbol": "☀️", "color": "#FDCB6E"},
    {"id": "20", "name": "Le Jugement", "symbol": "📯", "color": "#FD79A8"},
    {"id": "21", "name": "Le Monde", "symbol": "🌍", "color": "#00B894"},
    
    # Coupes
    {"id": "cups-01", "name": "As de Coupe", "symbol": "🏆", "color": "#74B9FF"},
    {"id": "cups-02", "name": "Deux de Coupe", "symbol": "💑", "color": "#74B9FF"},
    {"id": "cups-03", "name": "Trois de Coupe", "symbol": "🥂", "color": "#74B9FF"},
    {"id": "cups-04", "name": "Quatre de Coupe", "symbol": "🤔", "color": "#74B9FF"},
    {"id": "cups-05", "name": "Cinq de Coupe", "symbol": "😢", "color": "#74B9FF"},
    {"id": "cups-06", "name": "Six de Coupe", "symbol": "🎁", "color": "#74B9FF"},
    {"id": "cups-07", "name": "Sept de Coupe", "symbol": "💭", "color": "#74B9FF"},
    {"id": "cups-08", "name": "Huit de Coupe", "symbol": "🚶", "color": "#74B9FF"},
    {"id": "cups-09", "name": "Neuf de Coupe", "symbol": "😊", "color": "#74B9FF"},
    {"id": "cups-10", "name": "Dix de Coupe", "symbol": "👨‍👩‍👧‍👦", "color": "#74B9FF"},
    {"id": "cups-11", "name": "Valet de Coupe", "symbol": "🧑‍🎨", "color": "#74B9FF"},
    {"id": "cups-12", "name": "Cavalier de Coupe", "symbol": "🏇", "color": "#74B9FF"},
    {"id": "cups-13", "name": "Reine de Coupe", "symbol": "👸", "color": "#74B9FF"},
    {"id": "cups-14", "name": "Roi de Coupe", "symbol": "🤴", "color": "#74B9FF"},
    
    # Épées
    {"id": "swords-01", "name": "As d'Épée", "symbol": "⚔️", "color": "#636E72"},
    {"id": "swords-02", "name": "Deux d'Épée", "symbol": "🤷", "color": "#636E72"},
    {"id": "swords-03", "name": "Trois d'Épée", "symbol": "💔", "color": "#636E72"},
    {"id": "swords-04", "name": "Quatre d'Épée", "symbol": "😴", "color": "#636E72"},
    {"id": "swords-05", "name": "Cinq d'Épée", "symbol": "⚔️", "color": "#636E72"},
    {"id": "swords-06", "name": "Six d'Épée", "symbol": "⛵", "color": "#636E72"},
    {"id": "swords-07", "name": "Sept d'Épée", "symbol": "🥷", "color": "#636E72"},
    {"id": "swords-08", "name": "Huit d'Épée", "symbol": "🪢", "color": "#636E72"},
    {"id": "swords-09", "name": "Neuf d'Épée", "symbol": "😰", "color": "#636E72"},
    {"id": "swords-10", "name": "Dix d'Épée", "symbol": "💀", "color": "#636E72"},
    {"id": "swords-11", "name": "Valet d'Épée", "symbol": "🧑‍⚔️", "color": "#636E72"},
    {"id": "swords-12", "name": "Cavalier d'Épée", "symbol": "🏇", "color": "#636E72"},
    {"id": "swords-13", "name": "Reine d'Épée", "symbol": "👸", "color": "#636E72"},
    {"id": "swords-14", "name": "Roi d'Épée", "symbol": "🤴", "color": "#636E72"},
    
    # Bâtons
    {"id": "wands-01", "name": "As de Bâton", "symbol": "🪄", "color": "#E17055"},
    {"id": "wands-02", "name": "Deux de Bâton", "symbol": "🗺️", "color": "#E17055"},
    {"id": "wands-03", "name": "Trois de Bâton", "symbol": "🔭", "color": "#E17055"},
    {"id": "wands-04", "name": "Quatre de Bâton", "symbol": "🎉", "color": "#E17055"},
    {"id": "wands-05", "name": "Cinq de Bâton", "symbol": "⚔️", "color": "#E17055"},
    {"id": "wands-06", "name": "Six de Bâton", "symbol": "🏆", "color": "#E17055"},
    {"id": "wands-07", "name": "Sept de Bâton", "symbol": "🛡️", "color": "#E17055"},
    {"id": "wands-08", "name": "Huit de Bâton", "symbol": "💨", "color": "#E17055"},
    {"id": "wands-09", "name": "Neuf de Bâton", "symbol": "🛡️", "color": "#E17055"},
    {"id": "wands-10", "name": "Dix de Bâton", "symbol": "🎒", "color": "#E17055"},
    {"id": "wands-11", "name": "Valet de Bâton", "symbol": "🧑‍🌾", "color": "#E17055"},
    {"id": "wands-12", "name": "Cavalier de Bâton", "symbol": "🏇", "color": "#E17055"},
    {"id": "wands-13", "name": "Reine de Bâton", "symbol": "👸", "color": "#E17055"},
    {"id": "wands-14", "name": "Roi de Bâton", "symbol": "🤴", "color": "#E17055"},
    
    # Deniers
    {"id": "pentacles-01", "name": "As de Denier", "symbol": "🪙", "color": "#00B894"},
    {"id": "pentacles-02", "name": "Deux de Denier", "symbol": "⚖️", "color": "#00B894"},
    {"id": "pentacles-03", "name": "Trois de Denier", "symbol": "🏗️", "color": "#00B894"},
    {"id": "pentacles-04", "name": "Quatre de Denier", "symbol": "💰", "color": "#00B894"},
    {"id": "pentacles-05", "name": "Cinq de Denier", "symbol": "🏚️", "color": "#00B894"},
    {"id": "pentacles-06", "name": "Six de Denier", "symbol": "🤝", "color": "#00B894"},
    {"id": "pentacles-07", "name": "Sept de Denier", "symbol": "🌱", "color": "#00B894"},
    {"id": "pentacles-08", "name": "Huit de Denier", "symbol": "🔨", "color": "#00B894"},
    {"id": "pentacles-09", "name": "Neuf de Denier", "symbol": "🏡", "color": "#00B894"},
    {"id": "pentacles-10", "name": "Dix de Denier", "symbol": "👨‍👩‍👧‍👦", "color": "#00B894"},
    {"id": "pentacles-11", "name": "Valet de Denier", "symbol": "🧑‍🎓", "color": "#00B894"},
    {"id": "pentacles-12", "name": "Cavalier de Denier", "symbol": "🏇", "color": "#00B894"},
    {"id": "pentacles-13", "name": "Reine de Denier", "symbol": "👸", "color": "#00B894"},
    {"id": "pentacles-14", "name": "Roi de Denier", "symbol": "🤴", "color": "#00B894"},
]

def create_card_image(card):
    """Crée une image de carte de tarot stylisée"""
    try:
        # Dimensions de la carte
        width, height = 200, 300
        
        # Créer une nouvelle image
        img = Image.new('RGB', (width, height), color='#1a1a2e')
        draw = ImageDraw.Draw(img)
        
        # Bordure dorée
        border_color = '#d4af37'
        draw.rectangle([5, 5, width-5, height-5], outline=border_color, width=3)
        draw.rectangle([10, 10, width-10, height-10], outline=border_color, width=1)
        
        # Fond de la carte avec gradient simulé
        card_color = card['color']
        for y in range(20, height-20):
            alpha = int(255 * (1 - (y-20)/(height-40) * 0.3))
            color = card_color
            draw.line([(20, y), (width-20, y)], fill=color, width=1)
        
        # Symbole principal (grand)
        symbol_size = 60
        symbol_x = width // 2
        symbol_y = height // 2 - 20
        
        # Simuler l'affichage du symbole (en réalité, on utiliserait une police emoji)
        draw.ellipse([symbol_x-30, symbol_y-30, symbol_x+30, symbol_y+30], 
                    fill='#ffffff', outline=border_color, width=2)
        
        # Nom de la carte en bas
        name_y = height - 50
        text_width = len(card['name']) * 8
        text_x = (width - text_width) // 2
        
        # Simuler le texte (rectangle blanc pour le nom)
        draw.rectangle([text_x-5, name_y-10, text_x+text_width+5, name_y+15], 
                      fill='#ffffff', outline=border_color, width=1)
        
        # Numéro de la carte en haut
        number_text = card['id']
        draw.rectangle([15, 15, 45, 35], fill='#ffffff', outline=border_color, width=1)
        
        # Sauvegarder l'image
        filename = f"{card['id']}-{card['name'].lower().replace(' ', '-').replace("'", '')}.jpg"
        filepath = os.path.join('static/images/tarot', filename)
        img.save(filepath, 'JPEG', quality=95)
        
        print(f"✓ {filename} créé avec succès")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la création de {card['name']}: {e}")
        return False

def create_card_back():
    """Crée l'image du dos de carte"""
    try:
        width, height = 200, 300
        img = Image.new('RGB', (width, height), color='#1a1a2e')
        draw = ImageDraw.Draw(img)
        
        # Bordure dorée
        border_color = '#d4af37'
        draw.rectangle([5, 5, width-5, height-5], outline=border_color, width=3)
        
        # Motif mystique au centre
        center_x, center_y = width // 2, height // 2
        
        # Cercles concentriques
        for i in range(5):
            radius = 20 + i * 15
            draw.ellipse([center_x-radius, center_y-radius, center_x+radius, center_y+radius], 
                        outline=border_color, width=2)
        
        # Étoile au centre
        star_points = []
        for i in range(8):
            angle = i * 45
            x = center_x + 25 * (1 if i % 2 == 0 else 0.5) * (1 if angle < 180 else -1)
            y = center_y + 25 * (1 if i % 2 == 0 else 0.5) * (1 if 90 < angle < 270 else -1)
            star_points.append((x, y))
        
        # Motifs décoratifs dans les coins
        corner_size = 20
        corners = [(corner_size, corner_size), (width-corner_size, corner_size), 
                  (corner_size, height-corner_size), (width-corner_size, height-corner_size)]
        
        for corner in corners:
            draw.ellipse([corner[0]-10, corner[1]-10, corner[0]+10, corner[1]+10], 
                        outline=border_color, width=2)
        
        # Sauvegarder
        filepath = os.path.join('static/images/tarot', 'card-back.jpg')
        img.save(filepath, 'JPEG', quality=95)
        
        print("✓ Dos de carte créé avec succès")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la création du dos de carte: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔮 Création des cartes de tarot stylisées...")
    print(f"📁 Dossier de destination: static/images/tarot")
    print(f"📊 Nombre de cartes à créer: {len(tarot_cards)}")
    print("-" * 50)
    
    success_count = 0
    failed_count = 0
    
    # Créer le dos de carte
    if create_card_back():
        success_count += 1
    else:
        failed_count += 1
    
    # Créer toutes les cartes
    for i, card in enumerate(tarot_cards, 1):
        print(f"[{i}/{len(tarot_cards)}] ", end="")
        
        if create_card_image(card):
            success_count += 1
        else:
            failed_count += 1
    
    print("-" * 50)
    print(f"✅ Création terminée!")
    print(f"📈 Succès: {success_count}")
    print(f"📉 Échecs: {failed_count}")
    print(f"📊 Total: {len(tarot_cards) + 1}")

if __name__ == "__main__":
    main()

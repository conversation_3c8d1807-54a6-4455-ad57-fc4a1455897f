from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import <PERSON>gin<PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import json
import openai
import random

from config import Config
from models import db, User, Course, Module, Lesson, Product, Order, OrderItem, DreamInterpretation, TarotReading, ForumPost, ForumReply, Enrollment

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/images', exist_ok=True)

# Tarot cards data
TAROT_CARDS = [
    {"name": "Le Bateleur", "number": 1, "meaning": "Nouveau départ, potentiel, habileté"},
    {"name": "La Papesse", "number": 2, "meaning": "Intuition, mystère, sagesse intérieure"},
    {"name": "L'Impératrice", "number": 3, "meaning": "Créativité, fertilité, abondance"},
    {"name": "L'Empereur", "number": 4, "meaning": "Autorité, structure, contrôle"},
    {"name": "Le Pape", "number": 5, "meaning": "Tradition, enseignement spirituel, guidance"},
    {"name": "L'Amoureux", "number": 6, "meaning": "Choix, amour, harmonie"},
    {"name": "Le Chariot", "number": 7, "meaning": "Volonté, détermination, victoire"},
    {"name": "La Justice", "number": 8, "meaning": "Équilibre, vérité, karma"},
    {"name": "L'Hermite", "number": 9, "meaning": "Introspection, sagesse, guidance intérieure"},
    {"name": "La Roue de Fortune", "number": 10, "meaning": "Changement, cycles, destinée"},
    {"name": "La Force", "number": 11, "meaning": "Courage, maîtrise de soi, force intérieure"},
    {"name": "Le Pendu", "number": 12, "meaning": "Sacrifice, nouvelle perspective, patience"},
    {"name": "La Mort", "number": 13, "meaning": "Transformation, fin d'un cycle, renaissance"},
    {"name": "Tempérance", "number": 14, "meaning": "Modération, patience, guérison"},
    {"name": "Le Diable", "number": 15, "meaning": "Tentations, attachements, libération"},
    {"name": "La Maison Dieu", "number": 16, "meaning": "Révélation soudaine, changement brutal"},
    {"name": "L'Étoile", "number": 17, "meaning": "Espoir, inspiration, guidance spirituelle"},
    {"name": "La Lune", "number": 18, "meaning": "Illusions, intuition, subconscient"},
    {"name": "Le Soleil", "number": 19, "meaning": "Joie, succès, vitalité"},
    {"name": "Le Jugement", "number": 20, "meaning": "Renaissance, réveil spirituel, pardon"},
    {"name": "Le Monde", "number": 21, "meaning": "Accomplissement, réalisation, complétude"}
]

@app.route('/')
def index():
    featured_courses = Course.query.filter_by(is_published=True).limit(3).all()
    return render_template('index.html', featured_courses=featured_courses)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        subscription_level = request.form.get('subscription_level', 'standard')
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('Ce nom d\'utilisateur existe déjà.', 'error')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Cette adresse email est déjà utilisée.', 'error')
            return render_template('register.html')
        
        # Create new user
        user = User(username=username, email=email, subscription_level=subscription_level)
        user.set_password(password)
        
        # Set subscription expiry
        if subscription_level != 'standard':
            user.subscription_expires = datetime.utcnow() + timedelta(days=30)
        
        db.session.add(user)
        db.session.commit()
        
        login_user(user)
        flash('Inscription réussie ! Bienvenue dans Temple Du Voile.', 'success')
        return redirect(url_for('dashboard'))
    
    return render_template('register.html', subscription_levels=app.config['SUBSCRIPTION_LEVELS'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user, remember=True)
            next_page = request.args.get('next')
            flash('Connexion réussie !', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Vous avez été déconnecté.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    user_courses = db.session.query(Course).join(Enrollment).filter(Enrollment.user_id == current_user.id).all()
    recent_dreams = DreamInterpretation.query.filter_by(user_id=current_user.id).order_by(DreamInterpretation.created_at.desc()).limit(3).all()
    recent_tarot = TarotReading.query.filter_by(user_id=current_user.id).order_by(TarotReading.created_at.desc()).limit(3).all()
    
    return render_template('dashboard.html', 
                         user_courses=user_courses,
                         recent_dreams=recent_dreams,
                         recent_tarot=recent_tarot)

@app.route('/courses')
def courses():
    category = request.args.get('category')
    level = request.args.get('level')
    search = request.args.get('search')
    
    query = Course.query.filter_by(is_published=True)
    
    if category:
        query = query.filter(Course.category == category)
    if level:
        query = query.filter(Course.level == level)
    if search:
        query = query.filter(Course.title.contains(search))
    
    courses = query.all()
    categories = db.session.query(Course.category).distinct().all()
    
    return render_template('courses.html', 
                         courses=courses, 
                         categories=[c[0] for c in categories if c[0]])

@app.route('/course/<int:course_id>')
def course_detail(course_id):
    course = Course.query.get_or_404(course_id)
    is_enrolled = False
    can_access = False

    if current_user.is_authenticated:
        enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
        is_enrolled = enrollment is not None
        can_access = current_user.has_access_to_course(course)

    return render_template('course_detail.html',
                         course=course,
                         is_enrolled=is_enrolled,
                         can_access=can_access)

@app.route('/enroll/<int:course_id>')
@login_required
def enroll_course(course_id):
    course = Course.query.get_or_404(course_id)

    if not current_user.has_access_to_course(course):
        flash('Votre niveau d\'abonnement ne permet pas d\'accéder à ce cours.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    existing_enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
    if existing_enrollment:
        flash('Vous êtes déjà inscrit à ce cours.', 'info')
        return redirect(url_for('course_detail', course_id=course_id))

    enrollment = Enrollment(user_id=current_user.id, course_id=course_id)
    db.session.add(enrollment)
    db.session.commit()

    flash('Inscription au cours réussie !', 'success')
    return redirect(url_for('course_player', course_id=course_id))

@app.route('/course/<int:course_id>/player')
@login_required
def course_player(course_id):
    course = Course.query.get_or_404(course_id)
    enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()

    if not enrollment:
        flash('Vous devez vous inscrire à ce cours pour y accéder.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    return render_template('course_player.html', course=course, enrollment=enrollment)

# Esoteric Tools Routes
@app.route('/tools')
@login_required
def esoteric_tools():
    return render_template('tools/index.html')

@app.route('/tools/dreams', methods=['GET', 'POST'])
@login_required
def dream_interpretation():
    if request.method == 'POST':
        dream_description = request.form['dream_description']
        emotions = request.form.get('emotions', '')
        symbols = request.form.get('symbols', '')

        # Generate interpretation using OpenAI (simplified for demo)
        interpretation = generate_dream_interpretation(dream_description, emotions, symbols)

        # Save to database
        dream_record = DreamInterpretation(
            user_id=current_user.id,
            dream_description=dream_description,
            emotions=emotions,
            symbols=symbols,
            interpretation=interpretation
        )
        db.session.add(dream_record)
        db.session.commit()

        return render_template('tools/dream_result.html',
                             dream=dream_record)

    # Get user's dream history
    dreams = DreamInterpretation.query.filter_by(user_id=current_user.id)\
                                    .order_by(DreamInterpretation.created_at.desc())\
                                    .limit(10).all()

    return render_template('tools/dreams.html', dreams=dreams)

@app.route('/tools/tarot', methods=['GET', 'POST'])
@login_required
def tarot_reading():
    if request.method == 'POST':
        reading_type = request.form['reading_type']

        # Generate tarot reading
        cards, interpretation = generate_tarot_reading(reading_type)

        # Save to database
        reading_record = TarotReading(
            user_id=current_user.id,
            reading_type=reading_type,
            cards_drawn=json.dumps(cards),
            interpretation=interpretation
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/tarot_result.html',
                             reading=reading_record,
                             cards=cards)

    # Get user's reading history
    readings = TarotReading.query.filter_by(user_id=current_user.id)\
                                .order_by(TarotReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/tarot.html', readings=readings)

@app.route('/tools/horoscope', methods=['GET', 'POST'])
@login_required
def horoscope():
    if request.method == 'POST':
        birth_date = request.form['birth_date']
        birth_time = request.form.get('birth_time')
        birth_place = request.form['birth_place']

        # Update user's birth info
        current_user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        if birth_time:
            current_user.birth_time = datetime.strptime(birth_time, '%H:%M').time()
        current_user.birth_place = birth_place
        db.session.commit()

        # Generate horoscope
        horoscope_data = generate_horoscope(birth_date, birth_time, birth_place)

        return render_template('tools/horoscope_result.html',
                             horoscope=horoscope_data)

    return render_template('tools/horoscope.html')

# New pages routes
@app.route('/tarot')
def tarot():
    return render_template('tarot.html')

@app.route('/astrologie')
def astrologie():
    return render_template('astrologie.html')

@app.route('/numerologie')
def numerologie():
    return render_template('numerologie.html')

@app.route('/meditation')
def meditation():
    return render_template('meditation.html')

@app.route('/blog')
def blog():
    return render_template('blog.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

@app.route('/faq')
def faq():
    return render_template('faq.html')

@app.route('/aide')
def aide():
    return render_template('aide.html')

@app.route('/consultations')
def consultations():
    return render_template('consultations.html')

@app.route('/mentions-legales')
def mentions_legales():
    return render_template('mentions_legales.html')

@app.route('/politique-confidentialite')
def politique_confidentialite():
    return render_template('politique_confidentialite.html')

@app.route('/conditions-utilisation')
def conditions_utilisation():
    return render_template('conditions_utilisation.html')

@app.route('/cookies')
def cookies():
    return render_template('cookies.html')

def generate_dream_interpretation(description, emotions, symbols):
    """Generate dream interpretation using AI or predefined logic"""
    # Simplified interpretation logic for demo
    interpretations = [
        f"Votre rêve révèle des aspects profonds de votre subconscient. {description[:50]}... suggère une période de transformation personnelle.",
        f"Les symboles présents dans votre rêve indiquent une quête spirituelle. Les émotions ressenties ({emotions}) reflètent votre état intérieur actuel.",
        f"Ce rêve semble être un message de votre moi supérieur, vous guidant vers une meilleure compréhension de votre chemin de vie."
    ]
    return random.choice(interpretations)

def generate_tarot_reading(reading_type):
    """Generate tarot reading based on type"""
    if reading_type == 'single':
        cards = [random.choice(TAROT_CARDS)]
    elif reading_type == 'three_card':
        cards = random.sample(TAROT_CARDS, 3)
    else:  # celtic_cross
        cards = random.sample(TAROT_CARDS, 10)

    interpretation = f"Votre tirage révèle des énergies importantes. "
    for i, card in enumerate(cards):
        interpretation += f"La carte {card['name']} en position {i+1} indique: {card['meaning']}. "

    return cards, interpretation

def generate_horoscope(birth_date, birth_time, birth_place):
    """Generate horoscope data"""
    return {
        'sun_sign': 'Verseau',
        'moon_sign': 'Cancer',
        'rising_sign': 'Lion',
        'interpretation': 'Votre thème natal révèle une personnalité complexe et fascinante...'
    }

def create_sample_data():
    """Create sample courses and data for demonstration"""

    # Sample courses
    courses_data = [
        {
            'title': 'Introduction aux Sciences Occultes',
            'description': 'Découvrez les fondements des sciences occultes, leur histoire et leurs principales branches. Un cours essentiel pour débuter votre parcours initiatique.',
            'category': 'Sciences Occultes',
            'level': 'standard',
            'instructor': 'Maître Éliphas'
        },
        {
            'title': 'Les Mystères de la Kabbale',
            'description': 'Explorez la sagesse ancestrale de la Kabbale, ses symboles, l\'Arbre de Vie et les chemins de l\'illumination spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'premium',
            'instructor': 'Rabbi Sarah'
        },
        {
            'title': 'Parapsychologie Moderne',
            'description': 'Étudiez les phénomènes paranormaux avec une approche scientifique moderne. Télépathie, psychokinèse et perception extrasensorielle.',
            'category': 'Parapsychologie',
            'level': 'premium',
            'instructor': 'Dr. Michel Dubois'
        },
        {
            'title': 'Traditions Ésotériques Africaines',
            'description': 'Plongez dans la richesse des traditions spirituelles africaines, leurs rituels, leurs divinités et leur sagesse millénaire.',
            'category': 'Ésotérisme Africain',
            'level': 'gold',
            'instructor': 'Mama Aisha'
        },
        {
            'title': 'Maîtrise du Tarot de Marseille',
            'description': 'Apprenez à lire et interpréter le Tarot de Marseille. De l\'histoire des cartes aux tirages complexes.',
            'category': 'Tarot et Divination',
            'level': 'standard',
            'instructor': 'Madame Solange'
        },
        {
            'title': 'Alchimie et Transformation',
            'description': 'Découvrez l\'art royal de l\'alchimie, ses symboles hermétiques et les voies de la transmutation spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'gold',
            'instructor': 'Frère Nicolas'
        }
    ]

    for course_data in courses_data:
        existing_course = Course.query.filter_by(title=course_data['title']).first()
        if not existing_course:
            course = Course(**course_data)
            db.session.add(course)

            # Add sample modules for each course
            for i in range(1, 4):
                module = Module(
                    course=course,
                    title=f'Module {i}: {course_data["title"][:20]}...',
                    description=f'Description du module {i}',
                    order=i
                )
                db.session.add(module)

                # Add sample lessons for each module
                for j in range(1, 4):
                    lesson = Lesson(
                        module=module,
                        title=f'Leçon {j}: Introduction',
                        content=f'Contenu de la leçon {j} du module {i}',
                        order=j
                    )
                    db.session.add(lesson)

    # Sample products for marketplace
    products_data = [
        {
            'name': 'Jeu de Tarot de Marseille Authentique',
            'description': 'Jeu de tarot traditionnel de Marseille, reproduction fidèle des cartes historiques.',
            'price': 29.99,
            'category': 'Cartes et Oracles',
            'stock': 50
        },
        {
            'name': 'Pendule en Améthyste',
            'description': 'Pendule divinatoire en améthyste naturelle, parfait pour la radiesthésie.',
            'price': 45.00,
            'category': 'Outils Divinatoires',
            'stock': 25
        },
        {
            'name': 'Encens de Purification',
            'description': 'Mélange d\'encens pour purifier l\'espace et élever les vibrations.',
            'price': 15.99,
            'category': 'Encens et Résines',
            'stock': 100
        },
        {
            'name': 'Consultation Privée - 1h',
            'description': 'Consultation personnalisée avec un expert en sciences occultes.',
            'price': 80.00,
            'category': 'Consultations',
            'stock': 10
        }
    ]

    for product_data in products_data:
        existing_product = Product.query.filter_by(name=product_data['name']).first()
        if not existing_product:
            product = Product(**product_data)
            db.session.add(product)

    db.session.commit()
    print("Sample data created successfully!")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>', subscription_level='gold', is_admin=True)
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Admin user created: admin/admin123")

        # Create sample data
        create_sample_data()

    app.run(debug=True, host='0.0.0.0', port=5001)

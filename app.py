from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import json
import openai
import random
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from config import Config
from models import db, User, Course, Module, Lesson, Product, Order, OrderItem, DreamInterpretation, TarotReading, PalmReading, RuneReading, KabbalisticReading, KarmicReading, PendulumReading, ForumPost, ForumReply, Enrollment

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Add JSON filter for templates
@app.template_filter('from_json')
def from_json_filter(value):
    """Parse JSON string in templates"""
    try:
        return json.loads(value) if value else []
    except:
        return []
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/images', exist_ok=True)

# Tarot cards data
TAROT_CARDS = [
    {"name": "Le Bateleur", "number": 1, "meaning": "Nouveau départ, potentiel, habileté"},
    {"name": "La Papesse", "number": 2, "meaning": "Intuition, mystère, sagesse intérieure"},
    {"name": "L'Impératrice", "number": 3, "meaning": "Créativité, fertilité, abondance"},
    {"name": "L'Empereur", "number": 4, "meaning": "Autorité, structure, contrôle"},
    {"name": "Le Pape", "number": 5, "meaning": "Tradition, enseignement spirituel, guidance"},
    {"name": "L'Amoureux", "number": 6, "meaning": "Choix, amour, harmonie"},
    {"name": "Le Chariot", "number": 7, "meaning": "Volonté, détermination, victoire"},
    {"name": "La Justice", "number": 8, "meaning": "Équilibre, vérité, karma"},
    {"name": "L'Hermite", "number": 9, "meaning": "Introspection, sagesse, guidance intérieure"},
    {"name": "La Roue de Fortune", "number": 10, "meaning": "Changement, cycles, destinée"},
    {"name": "La Force", "number": 11, "meaning": "Courage, maîtrise de soi, force intérieure"},
    {"name": "Le Pendu", "number": 12, "meaning": "Sacrifice, nouvelle perspective, patience"},
    {"name": "La Mort", "number": 13, "meaning": "Transformation, fin d'un cycle, renaissance"},
    {"name": "Tempérance", "number": 14, "meaning": "Modération, patience, guérison"},
    {"name": "Le Diable", "number": 15, "meaning": "Tentations, attachements, libération"},
    {"name": "La Maison Dieu", "number": 16, "meaning": "Révélation soudaine, changement brutal"},
    {"name": "L'Étoile", "number": 17, "meaning": "Espoir, inspiration, guidance spirituelle"},
    {"name": "La Lune", "number": 18, "meaning": "Illusions, intuition, subconscient"},
    {"name": "Le Soleil", "number": 19, "meaning": "Joie, succès, vitalité"},
    {"name": "Le Jugement", "number": 20, "meaning": "Renaissance, réveil spirituel, pardon"},
    {"name": "Le Monde", "number": 21, "meaning": "Accomplissement, réalisation, complétude"}
]

@app.route('/')
def index():
    featured_courses = Course.query.filter_by(is_published=True).limit(3).all()
    return render_template('index.html', featured_courses=featured_courses)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        subscription_level = request.form.get('subscription_level', 'standard')
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('Ce nom d\'utilisateur existe déjà.', 'error')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Cette adresse email est déjà utilisée.', 'error')
            return render_template('register.html')
        
        # Create new user
        user = User(username=username, email=email, subscription_level=subscription_level)
        user.set_password(password)
        
        # Set subscription expiry
        if subscription_level != 'standard':
            user.subscription_expires = datetime.utcnow() + timedelta(days=30)
        
        db.session.add(user)
        db.session.commit()
        
        login_user(user)
        flash('Inscription réussie ! Bienvenue dans Temple Du Voile.', 'success')
        return redirect(url_for('dashboard'))
    
    return render_template('register.html', subscription_levels=app.config['SUBSCRIPTION_LEVELS'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user, remember=True)
            next_page = request.args.get('next')
            flash('Connexion réussie !', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Vous avez été déconnecté.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    user_courses = db.session.query(Course).join(Enrollment).filter(Enrollment.user_id == current_user.id).all()
    recent_dreams = DreamInterpretation.query.filter_by(user_id=current_user.id).order_by(DreamInterpretation.created_at.desc()).limit(3).all()
    recent_tarot = TarotReading.query.filter_by(user_id=current_user.id).order_by(TarotReading.created_at.desc()).limit(3).all()
    
    return render_template('dashboard.html', 
                         user_courses=user_courses,
                         recent_dreams=recent_dreams,
                         recent_tarot=recent_tarot)

@app.route('/courses')
def courses():
    category = request.args.get('category')
    level = request.args.get('level')
    search = request.args.get('search')
    
    query = Course.query.filter_by(is_published=True)
    
    if category:
        query = query.filter(Course.category == category)
    if level:
        query = query.filter(Course.level == level)
    if search:
        query = query.filter(Course.title.contains(search))
    
    courses = query.all()
    categories = db.session.query(Course.category).distinct().all()
    
    return render_template('courses.html', 
                         courses=courses, 
                         categories=[c[0] for c in categories if c[0]])

@app.route('/course/<int:course_id>')
def course_detail(course_id):
    course = Course.query.get_or_404(course_id)
    is_enrolled = False
    can_access = False

    if current_user.is_authenticated:
        enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
        is_enrolled = enrollment is not None
        can_access = current_user.has_access_to_course(course)

    return render_template('course_detail.html',
                         course=course,
                         is_enrolled=is_enrolled,
                         can_access=can_access)

@app.route('/enroll/<int:course_id>')
@login_required
def enroll_course(course_id):
    course = Course.query.get_or_404(course_id)

    if not current_user.has_access_to_course(course):
        flash('Votre niveau d\'abonnement ne permet pas d\'accéder à ce cours.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    existing_enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
    if existing_enrollment:
        flash('Vous êtes déjà inscrit à ce cours.', 'info')
        return redirect(url_for('course_detail', course_id=course_id))

    enrollment = Enrollment(user_id=current_user.id, course_id=course_id)
    db.session.add(enrollment)
    db.session.commit()

    flash('Inscription au cours réussie !', 'success')
    return redirect(url_for('course_player', course_id=course_id))

@app.route('/course/<int:course_id>/player')
@login_required
def course_player(course_id):
    course = Course.query.get_or_404(course_id)
    enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()

    if not enrollment:
        flash('Vous devez vous inscrire à ce cours pour y accéder.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    return render_template('course_player.html', course=course, enrollment=enrollment)

# Esoteric Tools Routes
@app.route('/tools')
@login_required
def esoteric_tools():
    return render_template('tools/index.html')

@app.route('/tools/dreams', methods=['GET', 'POST'])
@login_required
def dream_interpretation():
    if request.method == 'POST':
        dream_description = request.form['dream_description']
        emotions = request.form.get('emotions', '')
        symbols = request.form.get('symbols', '')

        # Generate interpretation using OpenAI (simplified for demo)
        interpretation = generate_dream_interpretation(dream_description, emotions, symbols)

        # Save to database
        dream_record = DreamInterpretation(
            user_id=current_user.id,
            dream_description=dream_description,
            emotions=emotions,
            symbols=symbols,
            interpretation=interpretation
        )
        db.session.add(dream_record)
        db.session.commit()

        return render_template('tools/dream_result.html',
                             dream=dream_record)

    # Get user's dream history
    dreams = DreamInterpretation.query.filter_by(user_id=current_user.id)\
                                    .order_by(DreamInterpretation.created_at.desc())\
                                    .limit(10).all()

    return render_template('tools/dreams.html', dreams=dreams)

@app.route('/tools/tarot', methods=['GET', 'POST'])
@login_required
def tarot_reading():
    if request.method == 'POST':
        reading_type = request.form['reading_type']
        question = request.form.get('question', 'Lecture générale')
        selected_cards_json = request.form.get('selected_cards')

        # Use AI to generate complete tarot reading
        selected_cards, interpretation = generate_ai_tarot_reading(reading_type, question)

        # Save to database
        reading_record = TarotReading(
            user_id=current_user.id,
            reading_type=reading_type,
            question=question,
            cards_drawn=json.dumps(selected_cards),
            interpretation=interpretation
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/tarot_result.html',
                             reading=reading_record)

    # Get user's reading history
    readings = TarotReading.query.filter_by(user_id=current_user.id)\
                                .order_by(TarotReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/tarot.html', readings=readings)

@app.route('/tools/palmistry', methods=['GET', 'POST'])
@login_required
def palmistry_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        hand_type = request.form['hand_type']
        question = request.form.get('question', '')

        # Handle file upload
        if 'palm_image' not in request.files:
            flash('Aucune image sélectionnée', 'error')
            return redirect(request.url)

        file = request.files['palm_image']
        if file.filename == '':
            flash('Aucune image sélectionnée', 'error')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            # Create uploads directory if it doesn't exist
            upload_dir = os.path.join(app.static_folder, 'uploads', 'palms')
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{current_user.id}_{filename}"
            filepath = os.path.join(upload_dir, filename)

            # Save file
            file.save(filepath)

            # Analyze palm with AI
            palm_analysis, interpretation, spiritual_advice, confidence = analyze_palm_with_ai(filepath, hand_type, question, client_name)

            # Save to database
            reading_record = PalmReading(
                user_id=current_user.id,
                client_name=client_name,
                hand_type=hand_type,
                image_filename=filename,
                question=question,
                palm_analysis=palm_analysis,
                interpretation=interpretation,
                spiritual_advice=spiritual_advice,
                confidence_score=confidence
            )
            db.session.add(reading_record)
            db.session.commit()

            return render_template('tools/palmistry_result.html', reading=reading_record)
        else:
            flash('Format de fichier non supporté', 'error')
            return redirect(request.url)

    # Get user's reading history
    readings = PalmReading.query.filter_by(user_id=current_user.id)\
                                .order_by(PalmReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/palmistry.html', readings=readings)

@app.route('/api/palm-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def palm_reading_api(reading_id):
    reading = PalmReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'hand_type': reading.hand_type,
            'image_filename': reading.image_filename,
            'question': reading.question,
            'palm_analysis': reading.palm_analysis,
            'interpretation': reading.interpretation,
            'spiritual_advice': reading.spiritual_advice,
            'confidence_score': reading.confidence_score,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        # Delete the image file
        if reading.image_filename:
            image_path = os.path.join(app.static_folder, 'uploads', 'palms', reading.image_filename)
            if os.path.exists(image_path):
                os.remove(image_path)

        # Delete the database record
        db.session.delete(reading)
        db.session.commit()

        return jsonify({'success': True})

@app.route('/download/palm-pdf/<int:reading_id>')
@login_required
def download_palm_pdf(reading_id):
    reading = PalmReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_palm_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="lecture_paume_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/runes', methods=['GET', 'POST'])
@login_required
def rune_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        reading_type = request.form['reading_type']
        question = request.form.get('question', '')

        # Generate rune reading
        runes_drawn, rune_analysis, interpretation, volva_guidance = generate_rune_reading(reading_type, question, client_name)

        # Save to database
        reading_record = RuneReading(
            user_id=current_user.id,
            client_name=client_name,
            reading_type=reading_type,
            question=question,
            runes_drawn=json.dumps(runes_drawn),
            rune_analysis=rune_analysis,
            interpretation=interpretation,
            volva_guidance=volva_guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        # Generate rune symbols for display
        rune_symbols = ''.join([rune['symbol'] for rune in runes_drawn])

        return render_template('tools/runes_result.html',
                             reading=reading_record,
                             rune_symbols=rune_symbols)

    # Get user's reading history
    readings = RuneReading.query.filter_by(user_id=current_user.id)\
                                .order_by(RuneReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/runes.html', readings=readings)

@app.route('/api/rune-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def rune_reading_api(reading_id):
    reading = RuneReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        runes = json.loads(reading.runes_drawn) if reading.runes_drawn else []
        rune_symbols = ''.join([rune['symbol'] for rune in runes])

        reading_type_display = {
            'single': 'Rune Unique',
            'three_rune': 'Trois Nornes',
            'five_rune': 'Croix d\'Odin',
            'nine_rune': 'Arbre Yggdrasil'
        }.get(reading.reading_type, reading.reading_type)

        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'reading_type': reading.reading_type,
            'reading_type_display': reading_type_display,
            'question': reading.question,
            'runes_symbols': rune_symbols,
            'rune_analysis': reading.rune_analysis,
            'interpretation': reading.interpretation,
            'volva_guidance': reading.volva_guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/rune-pdf/<int:reading_id>')
@login_required
def download_rune_pdf(reading_id):
    reading = RuneReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_rune_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="consultation_runique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/kabbalah', methods=['GET', 'POST'])
@login_required
def kabbalah_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        full_name = request.form['full_name']
        birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date()

        # Generate kabbalistic reading
        hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance = generate_kabbalistic_reading(
            full_name, birth_date, client_name
        )

        # Save to database
        reading_record = KabbalisticReading(
            user_id=current_user.id,
            client_name=client_name,
            full_name=full_name,
            birth_date=birth_date,
            hebrew_calculation=hebrew_calculation,
            destiny_number=destiny_number,
            life_path_analysis=life_path_analysis,
            spiritual_guidance=spiritual_guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/kabbalah_result.html', reading=reading_record)

    # Get user's reading history
    readings = KabbalisticReading.query.filter_by(user_id=current_user.id)\
                                      .order_by(KabbalisticReading.created_at.desc())\
                                      .limit(10).all()

    return render_template('tools/kabbalah.html', readings=readings)

@app.route('/api/kabbalah-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def kabbalah_reading_api(reading_id):
    reading = KabbalisticReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'full_name': reading.full_name,
            'birth_date': reading.birth_date.strftime('%d/%m/%Y'),
            'hebrew_calculation': reading.hebrew_calculation,
            'destiny_number': reading.destiny_number,
            'life_path_analysis': reading.life_path_analysis,
            'spiritual_guidance': reading.spiritual_guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/kabbalah-pdf/<int:reading_id>')
@login_required
def download_kabbalah_pdf(reading_id):
    reading = KabbalisticReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_kabbalah_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="chemin_kabbalistique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/karmic', methods=['GET', 'POST'])
@login_required
def karmic_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date()
        birth_time = request.form['birth_time']
        birth_place = request.form['birth_place']

        # Generate karmic reading
        karmic_analysis, past_lives, karmic_blocks, soul_mission = generate_karmic_reading(
            birth_date, birth_time, birth_place, client_name
        )

        # Save to database
        reading_record = KarmicReading(
            user_id=current_user.id,
            client_name=client_name,
            birth_date=birth_date,
            birth_time=birth_time,
            birth_place=birth_place,
            karmic_analysis=karmic_analysis,
            past_lives=past_lives,
            karmic_blocks=karmic_blocks,
            soul_mission=soul_mission
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/karmic_result.html', reading=reading_record)

    # Get user's reading history
    readings = KarmicReading.query.filter_by(user_id=current_user.id)\
                                  .order_by(KarmicReading.created_at.desc())\
                                  .limit(10).all()

    return render_template('tools/karmic.html', readings=readings)

@app.route('/api/karmic-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def karmic_reading_api(reading_id):
    reading = KarmicReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'birth_date': reading.birth_date.strftime('%d/%m/%Y'),
            'birth_time': reading.birth_time,
            'birth_place': reading.birth_place,
            'karmic_analysis': reading.karmic_analysis,
            'past_lives': reading.past_lives,
            'karmic_blocks': reading.karmic_blocks,
            'soul_mission': reading.soul_mission,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/karmic-pdf/<int:reading_id>')
@login_required
def download_karmic_pdf(reading_id):
    reading = KarmicReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_karmic_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="analyse_karmique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/pendulum', methods=['GET', 'POST'])
@login_required
def pendulum_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        question = request.form['question']
        pendulum_response = request.form.get('pendulum_response', '')

        # Generate pendulum reading
        energy_reading, guidance = generate_pendulum_reading(question, pendulum_response, client_name)

        # Save to database
        reading_record = PendulumReading(
            user_id=current_user.id,
            client_name=client_name,
            question=question,
            pendulum_response=pendulum_response,
            energy_reading=energy_reading,
            guidance=guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        # Return JSON for AJAX request
        return jsonify({
            'success': True,
            'energy_reading': energy_reading,
            'guidance': guidance,
            'pendulum_response': pendulum_response
        })

    # Get user's reading history
    readings = PendulumReading.query.filter_by(user_id=current_user.id)\
                                    .order_by(PendulumReading.created_at.desc())\
                                    .limit(10).all()

    return render_template('tools/pendulum.html', readings=readings)

@app.route('/api/pendulum-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def pendulum_reading_api(reading_id):
    reading = PendulumReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'question': reading.question,
            'pendulum_response': reading.pendulum_response,
            'energy_reading': reading.energy_reading,
            'guidance': reading.guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/tools/horoscope', methods=['GET', 'POST'])
@login_required
def horoscope():
    if request.method == 'POST':
        birth_date = request.form['birth_date']
        birth_time = request.form.get('birth_time')
        birth_place = request.form['birth_place']

        # Update user's birth info
        current_user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        if birth_time:
            current_user.birth_time = datetime.strptime(birth_time, '%H:%M').time()
        current_user.birth_place = birth_place
        db.session.commit()

        # Generate horoscope
        horoscope_data = generate_horoscope(birth_date, birth_time, birth_place)

        return render_template('tools/horoscope_result.html',
                             horoscope=horoscope_data)

    return render_template('tools/horoscope.html')

@app.route('/tools/numerology', methods=['GET', 'POST'])
@login_required
def numerology_reading():
    if request.method == 'POST':
        full_name = request.form['full_name']
        birth_date = request.form['birth_date']

        # Generate numerology reading
        numerology_data = generate_numerology_reading(full_name, birth_date)

        return render_template('tools/numerology_result.html',
                             numerology=numerology_data)

    return render_template('tools/numerology.html')

# New pages routes
@app.route('/tarot')
def tarot():
    return render_template('tarot.html')

@app.route('/astrologie')
def astrologie():
    return render_template('astrologie.html')

@app.route('/numerologie')
def numerologie():
    return render_template('numerologie.html')

@app.route('/meditation')
def meditation():
    return render_template('meditation.html')

@app.route('/blog')
def blog():
    return render_template('blog.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

@app.route('/faq')
def faq():
    return render_template('faq.html')

@app.route('/aide')
def aide():
    return render_template('aide.html')

@app.route('/consultations')
def consultations():
    return render_template('consultations.html')

@app.route('/mentions-legales')
def mentions_legales():
    return render_template('mentions_legales.html')

@app.route('/politique-confidentialite')
def politique_confidentialite():
    return render_template('politique_confidentialite.html')

@app.route('/conditions-utilisation')
def conditions_utilisation():
    return render_template('conditions_utilisation.html')

@app.route('/cookies')
def cookies():
    return render_template('cookies.html')

def generate_dream_interpretation(description, emotions, symbols):
    """Generate dream interpretation using AI only"""
    try:
        # Create comprehensive prompt for AI dream interpretation
        prompt = f"""
        En tant qu'expert en interprétation des rêves et psychologie jungienne, analysez ce rêve avec profondeur et bienveillance.

        DESCRIPTION DU RÊVE:
        {description}

        ÉMOTIONS RESSENTIES:
        {emotions if emotions else "Non spécifiées"}

        SYMBOLES MARQUANTS:
        {symbols if symbols else "Non spécifiés"}

        Fournissez une interprétation complète qui:
        1. Analyse les symboles principaux et leur signification
        2. Explore les émotions et leur lien avec la vie éveillée
        3. Révèle les messages de l'inconscient
        4. Donne des conseils pratiques pour intégrer ces insights
        5. Utilise un ton mystique mais accessible

        Structurez votre réponse en 3-4 paragraphes d'environ 400-500 mots au total.
        Commencez par "Votre rêve révèle..." et utilisez un langage poétique et inspirant.
        Mentionnez spécifiquement les émotions et symboles fournis dans votre analyse.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un expert en interprétation des rêves, formé en psychologie jungienne et en symbolisme onirique. Votre style est mystique, bienveillant et profondément spirituel. Vous aidez les gens à comprendre les messages de leur inconscient."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=600,
            temperature=0.8
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error generating AI dream interpretation: {e}")
        # Return error message instead of mock data
        return f"Je ne peux pas analyser votre rêve en ce moment en raison d'un problème technique. Votre rêve sur '{description[:50]}...' mérite une analyse approfondie. Veuillez réessayer dans quelques instants pour recevoir une interprétation personnalisée de vos symboles oniriques."

def get_all_tarot_cards():
    """Get complete tarot deck with image paths"""
    return [
        # Arcanes Majeurs
        {"id": "00", "name": "Le Mat", "meaning": "Nouveau départ, innocence, spontanéité", "image": "00-le-mat.jpg", "type": "major"},
        {"id": "01", "name": "Le Bateleur", "meaning": "Créativité, habileté, volonté", "image": "01-le-bateleur.jpg", "type": "major"},
        {"id": "02", "name": "La Papesse", "meaning": "Intuition, mystère, sagesse intérieure", "image": "02-la-papesse.jpg", "type": "major"},
        {"id": "03", "name": "L'Impératrice", "meaning": "Féminité, créativité, abondance", "image": "03-limpératrice.jpg", "type": "major"},
        {"id": "04", "name": "L'Empereur", "meaning": "Autorité, structure, contrôle", "image": "04-lempereur.jpg", "type": "major"},
        {"id": "05", "name": "Le Pape", "meaning": "Tradition, conformité, moralité", "image": "05-le-pape.jpg", "type": "major"},
        {"id": "06", "name": "L'Amoureux", "meaning": "Amour, choix, union", "image": "06-lamoureux.jpg", "type": "major"},
        {"id": "07", "name": "Le Chariot", "meaning": "Volonté, détermination, victoire", "image": "07-le-chariot.jpg", "type": "major"},
        {"id": "08", "name": "La Justice", "meaning": "Équité, vérité, cause et effet", "image": "08-la-justice.jpg", "type": "major"},
        {"id": "09", "name": "L'Hermite", "meaning": "Introspection, recherche, guidance intérieure", "image": "09-lhermite.jpg", "type": "major"},
        {"id": "10", "name": "La Roue de Fortune", "meaning": "Changement, cycles, destinée", "image": "10-la-roue-de-fortune.jpg", "type": "major"},
        {"id": "11", "name": "La Force", "meaning": "Force intérieure, courage, patience", "image": "11-la-force.jpg", "type": "major"},
        {"id": "12", "name": "Le Pendu", "meaning": "Sacrifice, attente, nouvelle perspective", "image": "12-le-pendu.jpg", "type": "major"},
        {"id": "13", "name": "L'Arcane sans Nom", "meaning": "Transformation, fin, renouveau", "image": "13-larcane-sans-nom.jpg", "type": "major"},
        {"id": "14", "name": "Tempérance", "meaning": "Modération, patience, but", "image": "14-tempérance.jpg", "type": "major"},
        {"id": "15", "name": "Le Diable", "meaning": "Bondage, addiction, sexualité", "image": "15-le-diable.jpg", "type": "major"},
        {"id": "16", "name": "La Maison Dieu", "meaning": "Révélation soudaine, bouleversement", "image": "16-la-maison-dieu.jpg", "type": "major"},
        {"id": "17", "name": "L'Étoile", "meaning": "Espoir, spiritualité, renouveau", "image": "17-létoile.jpg", "type": "major"},
        {"id": "18", "name": "La Lune", "meaning": "Illusion, peur, subconscient", "image": "18-la-lune.jpg", "type": "major"},
        {"id": "19", "name": "Le Soleil", "meaning": "Joie, succès, vitalité", "image": "19-le-soleil.jpg", "type": "major"},
        {"id": "20", "name": "Le Jugement", "meaning": "Jugement, renaissance, pardon", "image": "20-le-jugement.jpg", "type": "major"},
        {"id": "21", "name": "Le Monde", "meaning": "Accomplissement, voyage, succès", "image": "21-le-monde.jpg", "type": "major"},

        # Coupes
        {"id": "cups-01", "name": "As de Coupe", "meaning": "Nouveau départ émotionnel, amour", "image": "cups-01-as-de-coupe.jpg", "type": "cups"},
        {"id": "cups-02", "name": "Deux de Coupe", "meaning": "Partenariat, union, connexion", "image": "cups-02-deux-de-coupe.jpg", "type": "cups"},
        {"id": "cups-03", "name": "Trois de Coupe", "meaning": "Célébration, amitié, communauté", "image": "cups-03-trois-de-coupe.jpg", "type": "cups"},
        {"id": "cups-04", "name": "Quatre de Coupe", "meaning": "Apathie, contemplation, réévaluation", "image": "cups-04-quatre-de-coupe.jpg", "type": "cups"},
        {"id": "cups-05", "name": "Cinq de Coupe", "meaning": "Regret, perte, déception", "image": "cups-05-cinq-de-coupe.jpg", "type": "cups"},
        {"id": "cups-06", "name": "Six de Coupe", "meaning": "Nostalgie, enfance, innocence", "image": "cups-06-six-de-coupe.jpg", "type": "cups"},
        {"id": "cups-07", "name": "Sept de Coupe", "meaning": "Illusion, choix, rêverie", "image": "cups-07-sept-de-coupe.jpg", "type": "cups"},
        {"id": "cups-08", "name": "Huit de Coupe", "meaning": "Abandon, recherche spirituelle", "image": "cups-08-huit-de-coupe.jpg", "type": "cups"},
        {"id": "cups-09", "name": "Neuf de Coupe", "meaning": "Satisfaction, bonheur, accomplissement", "image": "cups-09-neuf-de-coupe.jpg", "type": "cups"},
        {"id": "cups-10", "name": "Dix de Coupe", "meaning": "Bonheur familial, harmonie", "image": "cups-10-dix-de-coupe.jpg", "type": "cups"},
        {"id": "cups-11", "name": "Valet de Coupe", "meaning": "Messager émotionnel, créativité", "image": "cups-11-valet-de-coupe.jpg", "type": "cups"},
        {"id": "cups-12", "name": "Cavalier de Coupe", "meaning": "Romance, charme, invitation", "image": "cups-12-cavalier-de-coupe.jpg", "type": "cups"},
        {"id": "cups-13", "name": "Reine de Coupe", "meaning": "Intuition, compassion, sécurité émotionnelle", "image": "cups-13-reine-de-coupe.jpg", "type": "cups"},
        {"id": "cups-14", "name": "Roi de Coupe", "meaning": "Maîtrise émotionnelle, diplomatie", "image": "cups-14-roi-de-coupe.jpg", "type": "cups"}
    ]

def generate_ai_tarot_reading(reading_type, question):
    """Generate complete tarot reading using AI - including card selection and interpretation"""
    try:
        # Determine number of cards
        num_cards = {
            'single': 1,
            'three_card': 3,
            'celtic_cross': 10
        }.get(reading_type, 1)

        # Create comprehensive prompt for AI to select cards and interpret
        prompt = f"""
        En tant que maître tarologue français expert, effectuez une lecture de tarot complète.

        ÉTAPE 1 - SÉLECTION DES CARTES:
        Choisissez {num_cards} carte(s) du tarot qui correspondent spirituellement à la situation.
        Question posée: {question if question else "Guidance générale"}
        Type de tirage: {reading_type}

        ÉTAPE 2 - INTERPRÉTATION COMPLÈTE:
        Fournissez une interprétation détaillée et spirituelle.

        FORMAT DE RÉPONSE REQUIS:

        CARTES_SELECTIONNEES:
        [Listez chaque carte avec: Nom de la carte|Signification|Position dans le tirage]

        INTERPRETATION_COMPLETE:
        [Votre interprétation complète du tirage, en analysant chaque carte et leurs interactions]

        Utilisez un ton mystique et bienveillant, en français.
        Soyez précis dans la sélection des cartes - choisissez celles qui résonnent avec la question.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître tarologue français expert en divination et spiritualité. Vous possédez une connaissance approfondie du tarot de Marseille et des arcanes majeurs et mineurs. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.8
        )

        full_response = response.choices[0].message.content

        # Parse the AI response to extract cards and interpretation
        selected_cards = []
        interpretation = ""

        if "CARTES_SELECTIONNEES:" in full_response:
            sections = full_response.split("CARTES_SELECTIONNEES:")
            if len(sections) > 1:
                cards_section = sections[1].split("INTERPRETATION_COMPLETE:")[0].strip()

                # Parse cards from AI response
                card_lines = [line.strip() for line in cards_section.split('\n') if line.strip() and '|' in line]
                for i, line in enumerate(card_lines):
                    if i < num_cards:
                        parts = line.split('|')
                        if len(parts) >= 2:
                            card = {
                                'id': f"ai-{i}",
                                'name': parts[0].strip(),
                                'meaning': parts[1].strip() if len(parts) > 1 else "Guidance spirituelle",
                                'position': parts[2].strip() if len(parts) > 2 else f"Position {i+1}",
                                'image': "card-back.jpg"  # Use generic back since we don't have specific images
                            }
                            selected_cards.append(card)

        # Extract interpretation
        if "INTERPRETATION_COMPLETE:" in full_response:
            interpretation = full_response.split("INTERPRETATION_COMPLETE:")[1].strip()

        # Fallback if parsing failed
        if not selected_cards:
            # Use fallback cards from database
            tarot_cards = get_all_tarot_cards()
            selected_cards = random.sample(tarot_cards, num_cards)

        if not interpretation:
            interpretation = "Cette lecture révèle des énergies importantes pour votre chemin spirituel. Les cartes vous invitent à la réflexion et à l'action consciente."

        return selected_cards, interpretation

    except Exception as e:
        print(f"Error in AI tarot reading generation: {e}")
        # Fallback to database cards
        tarot_cards = get_all_tarot_cards()
        selected_cards = random.sample(tarot_cards, num_cards)
        interpretation = "Cette lecture révèle des énergies importantes pour votre chemin spirituel."
        return selected_cards, interpretation

def generate_random_cards(reading_type):
    """Generate random cards for fallback"""
    tarot_cards = get_all_tarot_cards()

    if reading_type == 'single':
        return [random.choice(tarot_cards)]
    elif reading_type == 'three_card':
        return random.sample(tarot_cards, 3)
    else:  # celtic_cross
        return random.sample(tarot_cards, 10)

def generate_ai_tarot_interpretation(cards, question, reading_type):
    """Generate AI-powered tarot interpretation using OpenAI"""
    try:
        # Prepare the prompt for OpenAI
        cards_description = ""
        for i, card in enumerate(cards):
            position_name = get_position_name(i, reading_type)
            cards_description += f"{position_name}: {card['name']} - {card['meaning']}\n"

        prompt = f"""
        En tant qu'expert en tarot français, interprétez ce tirage de tarot avec sagesse et bienveillance.

        Question posée: {question}
        Type de tirage: {reading_type}

        Cartes tirées:
        {cards_description}

        Fournissez une interprétation détaillée et personnalisée qui:
        1. Répond à la question posée
        2. Explique la signification de chaque carte dans sa position
        3. Donne des conseils pratiques et spirituels
        4. Utilise un ton mystique mais accessible
        5. Fait environ 300-400 mots

        Commencez par "Votre tirage révèle..." et utilisez un langage poétique et inspirant.
        """

        # Call OpenAI API with GPT-4o
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître tarologue français expert en divination et spiritualité. Vous possédez une connaissance approfondie du tarot de Marseille et des arcanes majeurs et mineurs. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=600,
            temperature=0.8
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error generating AI interpretation: {e}")
        # Return error message instead of mock data
        return f"Je ne peux pas interpréter votre tirage en ce moment en raison d'un problème technique. Votre question '{question}' mérite une analyse approfondie. Veuillez réessayer dans quelques instants pour recevoir une interprétation personnalisée de vos cartes."

def get_position_name(index, reading_type):
    """Get the name of the position based on reading type"""
    if reading_type == 'single':
        return "Carte unique"
    elif reading_type == 'three_card':
        positions = ["Passé", "Présent", "Futur"]
        return positions[index] if index < len(positions) else f"Position {index + 1}"
    else:  # celtic_cross
        positions = [
            "Situation actuelle", "Défi/Obstacle", "Passé lointain", "Futur possible",
            "Couronne/But", "Fondation", "Votre approche", "Influences extérieures",
            "Espoirs et peurs", "Résultat final"
        ]
        return positions[index] if index < len(positions) else f"Position {index + 1}"

# Removed basic interpretation function - now using only AI-generated content

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_palm_with_ai(image_path, hand_type, question, client_name):
    """Analyze palm using GPT-4o Vision"""
    try:
        import base64

        # Read and encode image
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Prepare prompt for palm reading
        prompt = f"""
        En tant qu'expert en chiromancie française, analysez cette image de paume avec précision et bienveillance pour {client_name}.

        Type de main: {hand_type}
        Question posée: {question if question else "Lecture générale"}

        Fournissez une analyse complète structurée en 3 sections distinctes:

        SECTION 1 - ANALYSE TECHNIQUE DES LIGNES:
        - Ligne de vie (vitalité, santé, longévité)
        - Ligne de tête (intelligence, pensée, créativité)
        - Ligne de cœur (émotions, relations, amour)
        - Ligne de destinée (carrière, succès, réalisations)
        - Forme de la main et type élémentaire (terre, air, feu, eau)
        - Monts palmaires (Vénus, Jupiter, Saturne, Apollon, Mercure)

        SECTION 2 - INTERPRÉTATION SPIRITUELLE:
        - Personnalité et traits de caractère révélés
        - Potentiels et talents cachés
        - Défis actuels et opportunités futures
        - Réponse spécifique à la question posée

        SECTION 3 - CONSEILS SPIRITUELS PERSONNALISÉS:
        - 5 conseils pratiques pour développer les potentiels identifiés
        - Guidance pour surmonter les défis révélés
        - Pratiques spirituelles recommandées
        - Affirmations positives personnalisées
        - Périodes favorables pour les changements

        Utilisez un ton mystique mais accessible, en français, avec environ 600-700 mots au total.
        Soyez précis, bienveillant et inspirant dans vos observations.
        Structurez clairement chaque section avec des titres.
        """

        # Call OpenAI API with vision
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "Vous êtes un maître chiromancien français expert en lecture de la paume. Vous possédez une connaissance approfondie des lignes de la main, des monts, et de leur signification spirituelle. Votre style est mystique, précis et bienveillant."
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=800,
            temperature=0.7
        )

        full_analysis = response.choices[0].message.content

        # Split analysis into three sections
        palm_analysis = ""
        interpretation = ""
        spiritual_advice = ""

        if "SECTION 1" in full_analysis and "SECTION 2" in full_analysis and "SECTION 3" in full_analysis:
            # Split by sections
            sections = full_analysis.split("SECTION")
            if len(sections) >= 4:
                palm_analysis = "SECTION" + sections[1].strip()
                interpretation = "SECTION" + sections[2].strip()
                spiritual_advice = "SECTION" + sections[3].strip()
            else:
                # Fallback if sections are not properly formatted
                palm_analysis = full_analysis[:len(full_analysis)//3]
                interpretation = full_analysis[len(full_analysis)//3:2*len(full_analysis)//3]
                spiritual_advice = full_analysis[2*len(full_analysis)//3:]
        else:
            # Fallback split for older format
            if "INTERPRÉTATION SPIRITUELLE" in full_analysis:
                parts = full_analysis.split("INTERPRÉTATION SPIRITUELLE")
                palm_analysis = parts[0].strip()
                remaining = "INTERPRÉTATION SPIRITUELLE" + parts[1].strip()

                if "CONSEILS" in remaining:
                    interp_parts = remaining.split("CONSEILS")
                    interpretation = interp_parts[0].strip()
                    spiritual_advice = "CONSEILS" + interp_parts[1].strip()
                else:
                    interpretation = remaining
                    spiritual_advice = "Méditez sur les révélations de votre paume et laissez votre intuition vous guider."
            else:
                # Basic fallback
                lines = full_analysis.split('\n')
                third = len(lines) // 3
                palm_analysis = '\n'.join(lines[:third])
                interpretation = '\n'.join(lines[third:2*third])
                spiritual_advice = '\n'.join(lines[2*third:])

        # Generate confidence score based on response quality
        confidence = min(0.95, len(full_analysis) / 1200.0 + 0.6)

        return palm_analysis, interpretation, spiritual_advice, confidence

    except Exception as e:
        print(f"Error analyzing palm with AI: {e}")
        # Return error message instead of mock data
        error_analysis = f"Impossible d'analyser votre paume en ce moment en raison d'un problème technique."
        error_interpretation = f"Votre main {hand_type} mérite une analyse experte. Veuillez réessayer dans quelques instants."
        error_advice = f"En attendant, méditez sur votre question : '{question}' et faites confiance à votre intuition."
        return error_analysis, error_interpretation, error_advice, 0.0

# Removed basic palm analysis function - now using only AI-generated content

def get_rune_database():
    """Get complete rune database with Elder Futhark"""
    return [
        {"symbol": "ᚠ", "name": "Fehu", "meaning": "Richesse, bétail, prospérité matérielle", "element": "Feu", "keywords": ["abondance", "succès", "énergie créatrice"]},
        {"symbol": "ᚢ", "name": "Uruz", "meaning": "Force primitive, taureau sauvage, vitalité", "element": "Terre", "keywords": ["force", "santé", "courage"]},
        {"symbol": "ᚦ", "name": "Thurisaz", "meaning": "Géant, épine, protection", "element": "Feu", "keywords": ["défense", "conflit", "transformation"]},
        {"symbol": "ᚨ", "name": "Ansuz", "meaning": "Dieu, souffle divin, communication", "element": "Air", "keywords": ["sagesse", "inspiration", "révélation"]},
        {"symbol": "ᚱ", "name": "Raidho", "meaning": "Voyage, chevauchée, mouvement", "element": "Air", "keywords": ["voyage", "progression", "rythme"]},
        {"symbol": "ᚲ", "name": "Kenaz", "meaning": "Torche, connaissance, illumination", "element": "Feu", "keywords": ["créativité", "inspiration", "transformation"]},
        {"symbol": "ᚷ", "name": "Gebo", "meaning": "Don, échange, partenariat", "element": "Air", "keywords": ["générosité", "équilibre", "relation"]},
        {"symbol": "ᚹ", "name": "Wunjo", "meaning": "Joie, bonheur, harmonie", "element": "Terre", "keywords": ["bonheur", "succès", "accomplissement"]},
        {"symbol": "ᚺ", "name": "Hagalaz", "meaning": "Grêle, destruction créatrice", "element": "Eau", "keywords": ["changement", "crise", "libération"]},
        {"symbol": "ᚾ", "name": "Nauthiz", "meaning": "Besoin, nécessité, contrainte", "element": "Feu", "keywords": ["patience", "endurance", "leçon"]},
        {"symbol": "ᛁ", "name": "Isa", "meaning": "Glace, stagnation, concentration", "element": "Eau", "keywords": ["patience", "introspection", "clarté"]},
        {"symbol": "ᛃ", "name": "Jera", "meaning": "Année, cycle, récolte", "element": "Terre", "keywords": ["patience", "récompense", "cycles naturels"]},
        {"symbol": "ᛇ", "name": "Eihwaz", "meaning": "If, endurance, protection", "element": "Terre", "keywords": ["protection", "endurance", "initiation"]},
        {"symbol": "ᛈ", "name": "Perthro", "meaning": "Dés, destin, mystère", "element": "Eau", "keywords": ["destin", "mystère", "révélation"]},
        {"symbol": "ᛉ", "name": "Algiz", "meaning": "Élan, protection, connexion divine", "element": "Air", "keywords": ["protection", "spiritualité", "guidance"]},
        {"symbol": "ᛊ", "name": "Sowilo", "meaning": "Soleil, victoire, énergie", "element": "Feu", "keywords": ["succès", "victoire", "énergie positive"]},
        {"symbol": "ᛏ", "name": "Tiwaz", "meaning": "Tyr, justice, sacrifice", "element": "Air", "keywords": ["justice", "courage", "leadership"]},
        {"symbol": "ᛒ", "name": "Berkano", "meaning": "Bouleau, croissance, féminité", "element": "Terre", "keywords": ["croissance", "fertilité", "nouveau départ"]},
        {"symbol": "ᛖ", "name": "Ehwaz", "meaning": "Cheval, partenariat, loyauté", "element": "Terre", "keywords": ["partenariat", "confiance", "progrès"]},
        {"symbol": "ᛗ", "name": "Mannaz", "meaning": "Homme, humanité, soi", "element": "Air", "keywords": ["humanité", "coopération", "intelligence"]},
        {"symbol": "ᛚ", "name": "Laguz", "meaning": "Eau, intuition, flux", "element": "Eau", "keywords": ["intuition", "émotions", "flux de vie"]},
        {"symbol": "ᛜ", "name": "Ingwaz", "meaning": "Ing, fertilité, potentiel", "element": "Terre", "keywords": ["fertilité", "potentiel", "gestation"]},
        {"symbol": "ᛞ", "name": "Dagaz", "meaning": "Jour, éveil, transformation", "element": "Feu", "keywords": ["éveil", "transformation", "nouveau jour"]},
        {"symbol": "ᛟ", "name": "Othala", "meaning": "Héritage, foyer, tradition", "element": "Terre", "keywords": ["héritage", "famille", "tradition"]}
    ]

def generate_rune_reading(reading_type, question, client_name):
    """Generate rune reading with AI interpretation"""
    # Generate complete rune reading using AI
    try:
        selected_runes, rune_analysis, interpretation, volva_guidance = generate_ai_rune_reading_complete(
            reading_type, question, client_name
        )
        return selected_runes, rune_analysis, interpretation, volva_guidance
    except Exception as e:
        print(f"Error generating AI rune reading: {e}")
        # If AI fails, return error message
        return [], "Erreur lors de la génération de la lecture runique", "Veuillez réessayer plus tard", "Les runes ne peuvent pas être consultées en ce moment"

def generate_ai_rune_reading_complete(reading_type, question, client_name):
    """Generate complete rune reading using AI - including rune selection and interpretation"""
    try:
        # Determine number of runes and positions
        num_runes = {
            'single': 1,
            'three_rune': 3,
            'five_rune': 5,
            'nine_rune': 9
        }.get(reading_type, 1)

        positions = get_rune_positions(reading_type)

        # Create comprehensive prompt for AI to select runes and interpret
        prompt = f"""
        En tant que Völva experte en divination runique nordique, effectuez une consultation complète pour {client_name}.

        ÉTAPE 1 - SÉLECTION DES RUNES:
        Choisissez {num_runes} rune(s) du Futhark Elder qui correspondent spirituellement à la situation de {client_name}.
        Question posée: {question if question else "Guidance générale"}
        Type de tirage: {reading_type}
        Positions: {', '.join(positions)}

        ÉTAPE 2 - ANALYSE ET INTERPRÉTATION:
        Fournissez une analyse complète structurée.

        FORMAT DE RÉPONSE REQUIS:

        RUNES_SELECTED:
        [Listez chaque rune sélectionnée avec: Symbole runique|Nom|Signification|Position]

        ANALYSE_RUNES:
        [Analyse détaillée des runes et de leurs interactions dans leurs positions]

        INTERPRETATION_SPIRITUELLE:
        [Interprétation spirituelle complète pour répondre à la question]

        GUIDANCE_VOLVA:
        [Conseils pratiques et guidance spirituelle basés sur la sagesse nordique]

        Utilisez un ton mystique et authentique, en français, avec des références à la mythologie nordique.
        Soyez précis dans la sélection des runes - choisissez celles qui résonnent vraiment avec la situation.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes une Völva, une voyante nordique experte en divination runique. Vous possédez une connaissance approfondie des 24 runes Elder Futhark et de leur signification spirituelle. Vous sélectionnez intuitivement les runes appropriées pour chaque consultation et fournissez des interprétations authentiques basées sur la tradition nordique."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.8
        )

        full_response = response.choices[0].message.content

        # Parse the AI response
        selected_runes = []
        rune_analysis = ""
        interpretation = ""
        volva_guidance = ""

        # Extract runes section
        if "RUNES_SELECTED:" in full_response:
            sections = full_response.split("RUNES_SELECTED:")
            if len(sections) > 1:
                runes_section = sections[1].split("ANALYSE_RUNES:")[0].strip()

                # Parse runes from AI response
                rune_lines = [line.strip() for line in runes_section.split('\n') if line.strip() and '|' in line]
                for i, line in enumerate(rune_lines):
                    if i < num_runes:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            rune = {
                                'symbol': parts[0].strip(),
                                'name': parts[1].strip(),
                                'meaning': parts[2].strip(),
                                'position': parts[3].strip(),
                                'keywords': [parts[2].strip()]
                            }
                            selected_runes.append(rune)

        # If AI didn't provide proper runes, use fallback
        if len(selected_runes) < num_runes:
            rune_database = get_rune_database()
            selected_runes = random.sample(rune_database, num_runes)
            for i, rune in enumerate(selected_runes):
                rune['position'] = positions[i] if i < len(positions) else f"Position {i+1}"

        # Extract analysis section
        if "ANALYSE_RUNES:" in full_response:
            analysis_section = full_response.split("ANALYSE_RUNES:")[1]
            if "INTERPRETATION_SPIRITUELLE:" in analysis_section:
                rune_analysis = analysis_section.split("INTERPRETATION_SPIRITUELLE:")[0].strip()
            else:
                rune_analysis = analysis_section.strip()

        # Extract interpretation section
        if "INTERPRETATION_SPIRITUELLE:" in full_response:
            interp_section = full_response.split("INTERPRETATION_SPIRITUELLE:")[1]
            if "GUIDANCE_VOLVA:" in interp_section:
                interpretation = interp_section.split("GUIDANCE_VOLVA:")[0].strip()
            else:
                interpretation = interp_section.strip()

        # Extract guidance section
        if "GUIDANCE_VOLVA:" in full_response:
            volva_guidance = full_response.split("GUIDANCE_VOLVA:")[1].strip()

        # Ensure we have content for all sections
        if not rune_analysis:
            rune_analysis = "Les runes sélectionnées révèlent des énergies importantes pour votre chemin spirituel."
        if not interpretation:
            interpretation = "Cette consultation runique apporte guidance et sagesse pour votre situation actuelle."
        if not volva_guidance:
            volva_guidance = "Méditez sur le message des runes et laissez leur sagesse vous guider."

        return selected_runes, rune_analysis, interpretation, volva_guidance

    except Exception as e:
        print(f"Error in AI rune reading generation: {e}")
        raise e

# Removed old functions - now using only AI-generated content

def get_rune_positions(reading_type):
    """Get position names for different reading types"""
    positions = {
        'single': ["Guidance Présente"],
        'three_rune': ["Passé (Urd)", "Présent (Verdandi)", "Futur (Skuld)"],
        'five_rune': ["Situation", "Défi", "Passé", "Futur", "Résultat"],
        'nine_rune': ["Asgard", "Alfheim", "Vanaheim", "Midgard", "Jotunheim", "Muspelheim", "Svartalfheim", "Helheim", "Niflheim"]
    }
    return positions.get(reading_type, [f"Position {i+1}" for i in range(9)])

def generate_palm_pdf(reading):
    """Generate PDF report for palm reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Lecture de Chiromancie", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Type de main:', reading.hand_type.title()],
            ['Date de lecture:', reading.created_at.strftime('%d/%m/%Y à %H:%M')],
            ['Score de confiance:', f"{reading.confidence_score*100:.0f}%" if reading.confidence_score else 'N/A']
        ]

        if reading.question:
            client_data.append(['Question posée:', reading.question])

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            # Remove HTML tags
            clean = re.sub('<.*?>', '', text)
            # Replace HTML entities
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Analysis section
        if reading.palm_analysis:
            story.append(Paragraph("ANALYSE DES LIGNES DE LA MAIN", subtitle_style))
            analysis_text = clean_html(reading.palm_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Interpretation section
        if reading.interpretation:
            story.append(Paragraph("INTERPRÉTATION SPIRITUELLE", subtitle_style))
            interpretation_text = clean_html(reading.interpretation)
            story.append(Paragraph(interpretation_text, body_style))
            story.append(Spacer(1, 20))

        # Spiritual advice section
        if reading.spiritual_advice:
            story.append(Paragraph("CONSEILS SPIRITUELS PERSONNALISÉS", subtitle_style))
            advice_text = clean_html(reading.spiritual_advice)
            story.append(Paragraph(advice_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Votre guide spirituel", footer_style))
        story.append(Paragraph("Cette lecture est à des fins de divertissement et de développement personnel", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating PDF: {e}")
        # Return a simple error PDF
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF")
        return buffer.getvalue()

def generate_rune_pdf(reading):
    """Generate PDF report for rune reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Consultation Runique Nordique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        reading_type_display = {
            'single': 'Rune Unique',
            'three_rune': 'Trois Nornes',
            'five_rune': 'Croix d\'Odin',
            'nine_rune': 'Arbre Yggdrasil'
        }.get(reading.reading_type, reading.reading_type)

        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Type de tirage:', reading_type_display],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        if reading.question:
            client_data.append(['Question aux Nornes:', reading.question])

        # Add runes drawn
        if reading.runes_drawn:
            runes = json.loads(reading.runes_drawn)
            rune_symbols = ' '.join([rune['symbol'] for rune in runes])
            rune_names = ', '.join([rune['name'] for rune in runes])
            client_data.append(['Runes tirées:', f"{rune_symbols} ({rune_names})"])

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Analysis section
        if reading.rune_analysis:
            story.append(Paragraph("ANALYSE DES RUNES SACRÉES", subtitle_style))
            analysis_text = clean_html(reading.rune_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Interpretation section
        if reading.interpretation:
            story.append(Paragraph("INTERPRÉTATION SPIRITUELLE", subtitle_style))
            interpretation_text = clean_html(reading.interpretation)
            story.append(Paragraph(interpretation_text, body_style))
            story.append(Spacer(1, 20))

        # Völva guidance section
        if reading.volva_guidance:
            story.append(Paragraph("GUIDANCE DE LA VÖLVA", subtitle_style))
            guidance_text = clean_html(reading.volva_guidance)
            story.append(Paragraph(guidance_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Nordique Ancestrale", footer_style))
        story.append(Paragraph("Que les runes vous guident sur le chemin de la sagesse", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating rune PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF runique")
        return buffer.getvalue()

def generate_kabbalah_pdf(reading):
    """Generate PDF report for kabbalistic reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Chemin de Vie Kabbalistique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Nom complet:', reading.full_name],
            ['Date de naissance:', reading.birth_date.strftime('%d/%m/%Y')],
            ['Nombre de destinée:', str(reading.destiny_number)],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Hebrew calculation section
        if reading.hebrew_calculation:
            story.append(Paragraph("CALCUL HÉBRAÏQUE", subtitle_style))
            calculation_text = clean_html(reading.hebrew_calculation)
            story.append(Paragraph(calculation_text, body_style))
            story.append(Spacer(1, 20))

        # Life path analysis section
        if reading.life_path_analysis:
            story.append(Paragraph("ANALYSE DU CHEMIN DE VIE", subtitle_style))
            analysis_text = clean_html(reading.life_path_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Spiritual guidance section
        if reading.spiritual_guidance:
            story.append(Paragraph("GUIDANCE SPIRITUELLE", subtitle_style))
            guidance_text = clean_html(reading.spiritual_guidance)
            story.append(Paragraph(guidance_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Kabbalistique", footer_style))
        story.append(Paragraph("Que la lumière divine illumine votre chemin", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating kabbalah PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF kabbalistique")
        return buffer.getvalue()

def generate_karmic_pdf(reading):
    """Generate PDF report for karmic reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7b1fa2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Analyse Karmique Védique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Date de naissance:', reading.birth_date.strftime('%d/%m/%Y')],
            ['Heure de naissance:', reading.birth_time],
            ['Lieu de naissance:', reading.birth_place],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Karmic analysis section
        if reading.karmic_analysis:
            story.append(Paragraph("ANALYSE KARMIQUE", subtitle_style))
            analysis_text = clean_html(reading.karmic_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Past lives section
        if reading.past_lives:
            story.append(Paragraph("VIES PASSÉES", subtitle_style))
            past_lives_text = clean_html(reading.past_lives)
            story.append(Paragraph(past_lives_text, body_style))
            story.append(Spacer(1, 20))

        # Karmic blocks section
        if reading.karmic_blocks:
            story.append(Paragraph("BLOCAGES KARMIQUES", subtitle_style))
            blocks_text = clean_html(reading.karmic_blocks)
            story.append(Paragraph(blocks_text, body_style))
            story.append(Spacer(1, 20))

        # Soul mission section
        if reading.soul_mission:
            story.append(Paragraph("MISSION D'ÂME", subtitle_style))
            mission_text = clean_html(reading.soul_mission)
            story.append(Paragraph(mission_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Karmique Védique", footer_style))
        story.append(Paragraph("Que votre âme trouve la paix et l'évolution", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating karmic PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF karmique")
        return buffer.getvalue()

def generate_kabbalistic_reading(full_name, birth_date, client_name):
    """Generate kabbalistic life path reading using AI"""
    try:
        # Calculate Hebrew letter values
        hebrew_calculation = calculate_hebrew_values(full_name)
        destiny_number = calculate_destiny_number(full_name)

        # Create comprehensive prompt for AI kabbalistic interpretation
        prompt = f"""
        En tant qu'expert en Kabbale et numérologie hébraïque, analysez le chemin de vie kabbalistique pour {client_name}.

        DONNÉES KABBALISTIQUES:
        Nom complet: {full_name}
        Date de naissance: {birth_date.strftime('%d/%m/%Y')}
        Calcul hébraïque: {hebrew_calculation}
        Nombre de destinée: {destiny_number}

        Fournissez une analyse complète structurée en 3 sections:

        SECTION 1 - ANALYSE DU CHEMIN DE VIE:
        - Signification spirituelle du nombre de destinée {destiny_number}
        - Interprétation des lettres hébraïques du nom
        - Mission spirituelle révélée par la Kabbale
        - Talents et dons cachés
        - Défis karmiques à surmonter

        SECTION 2 - GUIDANCE SPIRITUELLE:
        - Conseils pour accomplir sa mission
        - Pratiques spirituelles recommandées
        - Périodes favorables dans la vie
        - Relations et partenariats spirituels
        - Développement de la conscience

        Utilisez un ton mystique et respectueux de la tradition kabbalistique.
        Référencez l'Arbre de Vie, les Sephiroth et la sagesse hébraïque.
        Environ 500-600 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître kabbaliste expert en numérologie hébraïque et en spiritualité juive. Vous possédez une connaissance approfondie de l'Arbre de Vie, des Sephiroth et de la signification mystique des lettres hébraïques. Votre style est respectueux, mystique et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=700,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        life_path_analysis = ""
        spiritual_guidance = ""

        if "SECTION 1" in full_interpretation and "SECTION 2" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 3:
                life_path_analysis = "SECTION" + sections[1].strip()
                spiritual_guidance = "SECTION" + sections[2].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                half = len(lines) // 2
                life_path_analysis = '\n'.join(lines[:half])
                spiritual_guidance = '\n'.join(lines[half:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            half = len(lines) // 2
            life_path_analysis = '\n'.join(lines[:half])
            spiritual_guidance = '\n'.join(lines[half:])

        return hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance

    except Exception as e:
        print(f"Error generating kabbalistic reading: {e}")
        # Fallback
        hebrew_calculation = f"Calcul hébraïque pour {full_name}"
        destiny_number = calculate_destiny_number(full_name)
        life_path_analysis = f"Votre nombre de destinée {destiny_number} révèle un chemin spirituel unique."
        spiritual_guidance = "Méditez sur la sagesse de vos ancêtres et suivez votre intuition."
        return hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance

def calculate_hebrew_values(name):
    """Calculate Hebrew letter values for a name"""
    # Hebrew letter values (simplified mapping)
    hebrew_values = {
        'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8, 'I': 9,
        'J': 10, 'K': 20, 'L': 30, 'M': 40, 'N': 50, 'O': 60, 'P': 70, 'Q': 80,
        'R': 90, 'S': 100, 'T': 200, 'U': 300, 'V': 400, 'W': 500, 'X': 600,
        'Y': 700, 'Z': 800
    }

    calculation = ""
    total = 0

    for char in name.upper():
        if char.isalpha():
            value = hebrew_values.get(char, 0)
            total += value
            calculation += f"{char} = {value}, "

    calculation = calculation.rstrip(', ')
    calculation += f" | Total: {total}"

    return calculation

def calculate_destiny_number(name):
    """Calculate destiny number from name"""
    # Simple calculation based on name length and vowels
    vowels = 'AEIOU'
    vowel_count = sum(1 for char in name.upper() if char in vowels)
    consonant_count = sum(1 for char in name.upper() if char.isalpha() and char not in vowels)

    # Reduce to single digit
    total = vowel_count + consonant_count
    while total > 9:
        total = sum(int(digit) for digit in str(total))

    return total

def generate_karmic_reading(birth_date, birth_time, birth_place, client_name):
    """Generate karmic analysis using AI"""
    try:
        # Create comprehensive prompt for AI karmic interpretation
        prompt = f"""
        En tant qu'expert en astrologie védique et analyse karmique, analysez le karma de {client_name}.

        DONNÉES ASTROLOGIQUES:
        Date de naissance: {birth_date.strftime('%d/%m/%Y')}
        Heure de naissance: {birth_time}
        Lieu de naissance: {birth_place}

        Fournissez une analyse karmique complète structurée en 4 sections:

        SECTION 1 - ANALYSE KARMIQUE GÉNÉRALE:
        - Patterns karmiques principaux révélés par la date de naissance
        - Influences planétaires sur le karma
        - Leçons principales de cette incarnation
        - Énergie karmique dominante

        SECTION 2 - VIES PASSÉES:
        - 2-3 incarnations significatives révélées par l'astrologie
        - Talents et compétences acquis dans le passé
        - Relations karmiques importantes
        - Événements marquants des vies antérieures

        SECTION 3 - BLOCAGES KARMIQUES:
        - Obstacles spirituels à surmonter
        - Patterns répétitifs à briser
        - Peurs et limitations karmiques
        - Clés pour la libération

        SECTION 4 - MISSION D'ÂME:
        - Purpose spirituel de cette incarnation
        - Talents à développer et partager
        - Service à rendre à l'humanité
        - Chemin vers l'évolution spirituelle

        Utilisez un ton mystique et bienveillant, avec des références à l'astrologie védique.
        Environ 600-700 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître en astrologie védique et analyse karmique. Vous possédez une connaissance approfondie des lois du karma, de la réincarnation et de l'évolution spirituelle. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        karmic_analysis = ""
        past_lives = ""
        karmic_blocks = ""
        soul_mission = ""

        if "SECTION 1" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 5:
                karmic_analysis = "SECTION" + sections[1].strip()
                past_lives = "SECTION" + sections[2].strip()
                karmic_blocks = "SECTION" + sections[3].strip()
                soul_mission = "SECTION" + sections[4].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                quarter = len(lines) // 4
                karmic_analysis = '\n'.join(lines[:quarter])
                past_lives = '\n'.join(lines[quarter:2*quarter])
                karmic_blocks = '\n'.join(lines[2*quarter:3*quarter])
                soul_mission = '\n'.join(lines[3*quarter:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            quarter = len(lines) // 4
            karmic_analysis = '\n'.join(lines[:quarter])
            past_lives = '\n'.join(lines[quarter:2*quarter])
            karmic_blocks = '\n'.join(lines[2*quarter:3*quarter])
            soul_mission = '\n'.join(lines[3*quarter:])

        return karmic_analysis, past_lives, karmic_blocks, soul_mission

    except Exception as e:
        print(f"Error generating karmic reading: {e}")
        # Fallback
        karmic_analysis = f"Votre thème natal révèle des patterns karmiques importants pour {client_name}."
        past_lives = "Vos vies passées ont forgé votre âme et vos talents actuels."
        karmic_blocks = "Certains blocages demandent à être libérés pour votre évolution."
        soul_mission = "Votre mission d'âme vous appelle vers un service spirituel."
        return karmic_analysis, past_lives, karmic_blocks, soul_mission

def generate_pendulum_reading(question, pendulum_response, client_name):
    """Generate pendulum reading interpretation using AI"""
    try:
        # Create comprehensive prompt for AI pendulum interpretation
        prompt = f"""
        En tant qu'expert en radiesthésie et divination par pendule, interprétez cette consultation pour {client_name}.

        CONSULTATION PENDULE:
        Question posée: {question}
        Réponse du pendule: {pendulum_response.upper()}

        Fournissez une interprétation complète structurée en 2 sections:

        SECTION 1 - LECTURE ÉNERGÉTIQUE:
        - Signification énergétique de la réponse {pendulum_response.upper()}
        - Analyse des vibrations captées par le pendule
        - État énergétique actuel de la situation
        - Forces en présence (positives/négatives)
        - Timing et synchronicités

        SECTION 2 - GUIDANCE COMPLÉMENTAIRE:
        - Conseils pratiques basés sur la réponse
        - Actions recommandées ou à éviter
        - Signes à observer dans les prochains jours
        - Méditations ou pratiques spirituelles
        - Message de guidance supérieure

        Utilisez un ton mystique et bienveillant, avec des références à l'énergie subtile.
        Environ 300-400 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un expert en radiesthésie et divination par pendule. Vous possédez une connaissance approfondie des énergies subtiles et de l'interprétation des réponses du pendule. Votre style est mystique, bienveillant et profondément intuitif."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        energy_reading = ""
        guidance = ""

        if "SECTION 1" in full_interpretation and "SECTION 2" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 3:
                energy_reading = "SECTION" + sections[1].strip()
                guidance = "SECTION" + sections[2].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                half = len(lines) // 2
                energy_reading = '\n'.join(lines[:half])
                guidance = '\n'.join(lines[half:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            half = len(lines) // 2
            energy_reading = '\n'.join(lines[:half])
            guidance = '\n'.join(lines[half:])

        return energy_reading, guidance

    except Exception as e:
        print(f"Error generating pendulum reading: {e}")
        # Fallback
        response_meanings = {
            'oui': "L'énergie est favorable et vous encourage à avancer.",
            'non': "L'énergie suggère la prudence et la réflexion.",
            'peut-etre': "L'énergie est en transition, le timing n'est pas encore optimal."
        }
        energy_reading = response_meanings.get(pendulum_response, "L'énergie révèle des messages importants.")
        guidance = f"Méditez sur cette réponse et restez attentif aux signes de l'univers, {client_name}."
        return energy_reading, guidance

def generate_horoscope(birth_date, birth_time, birth_place):
    """Generate horoscope data"""
    return {
        'sun_sign': 'Verseau',
        'moon_sign': 'Cancer',
        'rising_sign': 'Lion',
        'interpretation': 'Votre thème natal révèle une personnalité complexe et fascinante...'
    }

def generate_numerology_reading(full_name, birth_date):
    """Generate numerology reading based on name and birth date"""
    from datetime import datetime

    # Calculate Life Path Number
    birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d')
    life_path = calculate_life_path(birth_date_obj)

    # Calculate Expression Number
    expression_number = calculate_expression_number(full_name)

    # Calculate Soul Urge Number
    soul_urge = calculate_soul_urge_number(full_name)

    # Calculate Personality Number
    personality_number = calculate_personality_number(full_name)

    return {
        'life_path': life_path,
        'expression_number': expression_number,
        'soul_urge': soul_urge,
        'personality_number': personality_number,
        'interpretation': generate_numerology_interpretation(life_path, expression_number)
    }

def calculate_life_path(birth_date):
    """Calculate life path number from birth date"""
    total = birth_date.day + birth_date.month + birth_date.year
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_expression_number(full_name):
    """Calculate expression number from full name"""
    letter_values = {
        'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8, 'I': 9,
        'J': 1, 'K': 2, 'L': 3, 'M': 4, 'N': 5, 'O': 6, 'P': 7, 'Q': 8, 'R': 9,
        'S': 1, 'T': 2, 'U': 3, 'V': 4, 'W': 5, 'X': 6, 'Y': 7, 'Z': 8
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name if char.isalpha())
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_soul_urge_number(full_name):
    """Calculate soul urge number from vowels in name"""
    vowels = 'AEIOU'
    letter_values = {
        'A': 1, 'E': 5, 'I': 9, 'O': 6, 'U': 3
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name if char.upper() in vowels)
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_personality_number(full_name):
    """Calculate personality number from consonants in name"""
    vowels = 'AEIOU'
    letter_values = {
        'B': 2, 'C': 3, 'D': 4, 'F': 6, 'G': 7, 'H': 8, 'J': 1, 'K': 2, 'L': 3,
        'M': 4, 'N': 5, 'P': 7, 'Q': 8, 'R': 9, 'S': 1, 'T': 2, 'V': 4, 'W': 5,
        'X': 6, 'Y': 7, 'Z': 8
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name
                if char.isalpha() and char.upper() not in vowels)
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def generate_numerology_interpretation(life_path, expression_number):
    """Generate interpretation based on numbers"""
    interpretations = {
        1: "Leader naturel, indépendant et pionnier",
        2: "Coopératif, diplomatique et sensible",
        3: "Créatif, expressif et optimiste",
        4: "Pratique, organisé et travailleur",
        5: "Aventurier, libre et polyvalent",
        6: "Protecteur, responsable et aimant",
        7: "Spirituel, analytique et introspectif",
        8: "Ambitieux, matérialiste et puissant",
        9: "Humanitaire, généreux et idéaliste",
        11: "Intuitif, inspiré et visionnaire",
        22: "Maître bâtisseur, pratique et visionnaire",
        33: "Maître enseignant, altruiste et guérisseur"
    }

    life_path_meaning = interpretations.get(life_path, "Nombre unique avec des qualités spéciales")
    expression_meaning = interpretations.get(expression_number, "Talents particuliers à développer")

    return f"Votre chemin de vie {life_path} indique que vous êtes {life_path_meaning}. " \
           f"Votre nombre d'expression {expression_number} révèle que vous avez {expression_meaning}."

def create_sample_data():
    """Create sample courses and data for demonstration"""

    # Sample courses
    courses_data = [
        {
            'title': 'Introduction aux Sciences Occultes',
            'description': 'Découvrez les fondements des sciences occultes, leur histoire et leurs principales branches. Un cours essentiel pour débuter votre parcours initiatique.',
            'category': 'Sciences Occultes',
            'level': 'standard',
            'instructor': 'Maître Éliphas'
        },
        {
            'title': 'Les Mystères de la Kabbale',
            'description': 'Explorez la sagesse ancestrale de la Kabbale, ses symboles, l\'Arbre de Vie et les chemins de l\'illumination spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'premium',
            'instructor': 'Rabbi Sarah'
        },
        {
            'title': 'Parapsychologie Moderne',
            'description': 'Étudiez les phénomènes paranormaux avec une approche scientifique moderne. Télépathie, psychokinèse et perception extrasensorielle.',
            'category': 'Parapsychologie',
            'level': 'premium',
            'instructor': 'Dr. Michel Dubois'
        },
        {
            'title': 'Traditions Ésotériques Africaines',
            'description': 'Plongez dans la richesse des traditions spirituelles africaines, leurs rituels, leurs divinités et leur sagesse millénaire.',
            'category': 'Ésotérisme Africain',
            'level': 'gold',
            'instructor': 'Mama Aisha'
        },
        {
            'title': 'Maîtrise du Tarot de Marseille',
            'description': 'Apprenez à lire et interpréter le Tarot de Marseille. De l\'histoire des cartes aux tirages complexes.',
            'category': 'Tarot et Divination',
            'level': 'standard',
            'instructor': 'Madame Solange'
        },
        {
            'title': 'Alchimie et Transformation',
            'description': 'Découvrez l\'art royal de l\'alchimie, ses symboles hermétiques et les voies de la transmutation spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'gold',
            'instructor': 'Frère Nicolas'
        }
    ]

    for course_data in courses_data:
        existing_course = Course.query.filter_by(title=course_data['title']).first()
        if not existing_course:
            course = Course(**course_data)
            db.session.add(course)

            # Add sample modules for each course
            for i in range(1, 4):
                module = Module(
                    course=course,
                    title=f'Module {i}: {course_data["title"][:20]}...',
                    description=f'Description du module {i}',
                    order=i
                )
                db.session.add(module)

                # Add sample lessons for each module
                for j in range(1, 4):
                    lesson = Lesson(
                        module=module,
                        title=f'Leçon {j}: Introduction',
                        content=f'Contenu de la leçon {j} du module {i}',
                        order=j
                    )
                    db.session.add(lesson)

    # Sample products for marketplace
    products_data = [
        {
            'name': 'Jeu de Tarot de Marseille Authentique',
            'description': 'Jeu de tarot traditionnel de Marseille, reproduction fidèle des cartes historiques.',
            'price': 29.99,
            'category': 'Cartes et Oracles',
            'stock': 50
        },
        {
            'name': 'Pendule en Améthyste',
            'description': 'Pendule divinatoire en améthyste naturelle, parfait pour la radiesthésie.',
            'price': 45.00,
            'category': 'Outils Divinatoires',
            'stock': 25
        },
        {
            'name': 'Encens de Purification',
            'description': 'Mélange d\'encens pour purifier l\'espace et élever les vibrations.',
            'price': 15.99,
            'category': 'Encens et Résines',
            'stock': 100
        },
        {
            'name': 'Consultation Privée - 1h',
            'description': 'Consultation personnalisée avec un expert en sciences occultes.',
            'price': 80.00,
            'category': 'Consultations',
            'stock': 10
        }
    ]

    for product_data in products_data:
        existing_product = Product.query.filter_by(name=product_data['name']).first()
        if not existing_product:
            product = Product(**product_data)
            db.session.add(product)

    db.session.commit()
    print("Sample data created successfully!")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>', subscription_level='gold', is_admin=True)
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Admin user created: admin/admin123")

        # Create sample data
        create_sample_data()

    app.run(debug=True, host='0.0.0.0', port=5001)

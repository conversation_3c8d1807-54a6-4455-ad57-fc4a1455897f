from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import json
import openai
import random
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from config import Config
from models import db, User, Course, Module, Lesson, Product, Order, OrderItem, DreamInterpretation, TarotReading, PalmReading, RuneReading, KabbalisticReading, KarmicReading, PendulumReading, ForumPost, ForumReply, Enrollment

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Add JSON filter for templates
@app.template_filter('from_json')
def from_json_filter(value):
    """Parse JSON string in templates"""
    try:
        return json.loads(value) if value else []
    except:
        return []

# Add nl2br filter for templates
@app.template_filter('nl2br')
def nl2br_filter(value):
    """Convert newlines to <br> tags"""
    if not value:
        return value
    return value.replace('\n', '<br>')

# Add safe filter for templates
@app.template_filter('safe')
def safe_filter(value):
    """Mark string as safe for HTML rendering"""
    from markupsafe import Markup
    return Markup(value)

# Add clean_html filter for templates
@app.template_filter('clean_html')
def clean_html_filter(value):
    """Remove HTML tags from text"""
    if not value:
        return value
    import re
    # Remove HTML tags
    clean_text = re.sub(r'<[^>]+>', '', value)
    # Replace HTML entities
    clean_text = clean_text.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    return clean_text
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/images', exist_ok=True)

# Tarot cards data
TAROT_CARDS = [
    {"name": "Le Bateleur", "number": 1, "meaning": "Nouveau départ, potentiel, habileté"},
    {"name": "La Papesse", "number": 2, "meaning": "Intuition, mystère, sagesse intérieure"},
    {"name": "L'Impératrice", "number": 3, "meaning": "Créativité, fertilité, abondance"},
    {"name": "L'Empereur", "number": 4, "meaning": "Autorité, structure, contrôle"},
    {"name": "Le Pape", "number": 5, "meaning": "Tradition, enseignement spirituel, guidance"},
    {"name": "L'Amoureux", "number": 6, "meaning": "Choix, amour, harmonie"},
    {"name": "Le Chariot", "number": 7, "meaning": "Volonté, détermination, victoire"},
    {"name": "La Justice", "number": 8, "meaning": "Équilibre, vérité, karma"},
    {"name": "L'Hermite", "number": 9, "meaning": "Introspection, sagesse, guidance intérieure"},
    {"name": "La Roue de Fortune", "number": 10, "meaning": "Changement, cycles, destinée"},
    {"name": "La Force", "number": 11, "meaning": "Courage, maîtrise de soi, force intérieure"},
    {"name": "Le Pendu", "number": 12, "meaning": "Sacrifice, nouvelle perspective, patience"},
    {"name": "La Mort", "number": 13, "meaning": "Transformation, fin d'un cycle, renaissance"},
    {"name": "Tempérance", "number": 14, "meaning": "Modération, patience, guérison"},
    {"name": "Le Diable", "number": 15, "meaning": "Tentations, attachements, libération"},
    {"name": "La Maison Dieu", "number": 16, "meaning": "Révélation soudaine, changement brutal"},
    {"name": "L'Étoile", "number": 17, "meaning": "Espoir, inspiration, guidance spirituelle"},
    {"name": "La Lune", "number": 18, "meaning": "Illusions, intuition, subconscient"},
    {"name": "Le Soleil", "number": 19, "meaning": "Joie, succès, vitalité"},
    {"name": "Le Jugement", "number": 20, "meaning": "Renaissance, réveil spirituel, pardon"},
    {"name": "Le Monde", "number": 21, "meaning": "Accomplissement, réalisation, complétude"}
]

@app.route('/')
def index():
    featured_courses = Course.query.filter_by(is_published=True).limit(3).all()
    return render_template('index.html', featured_courses=featured_courses)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        subscription_level = request.form.get('subscription_level', 'standard')
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('Ce nom d\'utilisateur existe déjà.', 'error')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Cette adresse email est déjà utilisée.', 'error')
            return render_template('register.html')
        
        # Create new user
        user = User(username=username, email=email, subscription_level=subscription_level)
        user.set_password(password)
        
        # Set subscription expiry
        if subscription_level != 'standard':
            user.subscription_expires = datetime.utcnow() + timedelta(days=30)
        
        db.session.add(user)
        db.session.commit()
        
        login_user(user)
        flash('Inscription réussie ! Bienvenue dans Temple Du Voile.', 'success')
        return redirect(url_for('dashboard'))
    
    return render_template('register.html', subscription_levels=app.config['SUBSCRIPTION_LEVELS'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user, remember=True)
            next_page = request.args.get('next')
            flash('Connexion réussie !', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Vous avez été déconnecté.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    user_courses = db.session.query(Course).join(Enrollment).filter(Enrollment.user_id == current_user.id).all()
    recent_dreams = DreamInterpretation.query.filter_by(user_id=current_user.id).order_by(DreamInterpretation.created_at.desc()).limit(3).all()
    recent_tarot = TarotReading.query.filter_by(user_id=current_user.id).order_by(TarotReading.created_at.desc()).limit(3).all()
    
    return render_template('dashboard.html', 
                         user_courses=user_courses,
                         recent_dreams=recent_dreams,
                         recent_tarot=recent_tarot)

@app.route('/courses')
def courses():
    category = request.args.get('category')
    level = request.args.get('level')
    search = request.args.get('search')
    
    query = Course.query.filter_by(is_published=True)
    
    if category:
        query = query.filter(Course.category == category)
    if level:
        query = query.filter(Course.level == level)
    if search:
        query = query.filter(Course.title.contains(search))
    
    courses = query.all()
    categories = db.session.query(Course.category).distinct().all()
    
    return render_template('courses.html', 
                         courses=courses, 
                         categories=[c[0] for c in categories if c[0]])

@app.route('/course/<int:course_id>')
def course_detail(course_id):
    course = Course.query.get_or_404(course_id)
    is_enrolled = False
    can_access = False

    if current_user.is_authenticated:
        enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
        is_enrolled = enrollment is not None
        can_access = current_user.has_access_to_course(course)

    return render_template('course_detail.html',
                         course=course,
                         is_enrolled=is_enrolled,
                         can_access=can_access)

@app.route('/enroll/<int:course_id>')
@login_required
def enroll_course(course_id):
    course = Course.query.get_or_404(course_id)

    if not current_user.has_access_to_course(course):
        flash('Votre niveau d\'abonnement ne permet pas d\'accéder à ce cours.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    existing_enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()
    if existing_enrollment:
        flash('Vous êtes déjà inscrit à ce cours.', 'info')
        return redirect(url_for('course_detail', course_id=course_id))

    enrollment = Enrollment(user_id=current_user.id, course_id=course_id)
    db.session.add(enrollment)
    db.session.commit()

    flash('Inscription au cours réussie !', 'success')
    return redirect(url_for('course_player', course_id=course_id))

@app.route('/course/<int:course_id>/player')
@login_required
def course_player(course_id):
    course = Course.query.get_or_404(course_id)
    enrollment = Enrollment.query.filter_by(user_id=current_user.id, course_id=course_id).first()

    if not enrollment:
        flash('Vous devez vous inscrire à ce cours pour y accéder.', 'error')
        return redirect(url_for('course_detail', course_id=course_id))

    return render_template('course_player.html', course=course, enrollment=enrollment)

# Esoteric Tools Routes
@app.route('/tools')
@login_required
def esoteric_tools():
    return render_template('tools/index.html')

@app.route('/tools/dreams', methods=['GET', 'POST'])
@login_required
def dream_interpretation():
    if request.method == 'POST':
        dream_description = request.form['dream_description']
        emotions = request.form.get('emotions', '')
        symbols = request.form.get('symbols', '')

        # Generate interpretation using OpenAI (simplified for demo)
        interpretation = generate_dream_interpretation(dream_description, emotions, symbols)

        # Save to database
        dream_record = DreamInterpretation(
            user_id=current_user.id,
            dream_description=dream_description,
            emotions=emotions,
            symbols=symbols,
            interpretation=interpretation
        )
        db.session.add(dream_record)
        db.session.commit()

        return render_template('tools/dream_result.html',
                             dream=dream_record)

    # Get user's dream history
    dreams = DreamInterpretation.query.filter_by(user_id=current_user.id)\
                                    .order_by(DreamInterpretation.created_at.desc())\
                                    .limit(10).all()

    return render_template('tools/dreams.html', dreams=dreams)

@app.route('/tools/dreams/result/<int:dream_id>')
@login_required
def dream_result(dream_id):
    """Display dream interpretation result"""
    dream = DreamInterpretation.query.filter_by(id=dream_id, user_id=current_user.id).first_or_404()
    return render_template('tools/dream_result.html', dream=dream)

@app.route('/api/dreams/<int:dream_id>', methods=['DELETE'])
@login_required
def delete_dream(dream_id):
    """Delete a dream interpretation"""
    try:
        dream = DreamInterpretation.query.filter_by(id=dream_id, user_id=current_user.id).first_or_404()
        db.session.delete(dream)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Interprétation supprimée avec succès'})
    except Exception as e:
        print(f"Error deleting dream: {e}")
        return jsonify({'success': False, 'message': 'Erreur lors de la suppression'}), 500

@app.route('/tools/dreams/download/<int:dream_id>')
@login_required
def download_dream_pdf(dream_id):
    """Download dream interpretation as PDF"""
    dream = DreamInterpretation.query.filter_by(id=dream_id, user_id=current_user.id).first_or_404()

    try:
        # Generate PDF content
        pdf_content = generate_dream_pdf(dream)

        # Create response
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="interpretation_reve_{dream.created_at.strftime("%Y%m%d_%H%M")}.pdf"'

        return response

    except Exception as e:
        print(f"Error generating dream PDF: {e}")
        flash('Erreur lors de la génération du PDF', 'error')
        return redirect(url_for('dream_result', dream_id=dream_id))

@app.route('/tools/tarot', methods=['GET', 'POST'])
@login_required
def tarot_reading():
    if request.method == 'POST':
        reading_type = request.form['reading_type']
        question = request.form.get('question', 'Lecture générale')
        selected_cards_json = request.form.get('selected_cards')

        # Use AI to generate complete tarot reading
        selected_cards, interpretation = generate_ai_tarot_reading(reading_type, question)

        # Save to database
        reading_record = TarotReading(
            user_id=current_user.id,
            reading_type=reading_type,
            question=question,
            cards_drawn=json.dumps(selected_cards),
            interpretation=interpretation
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/tarot_result.html',
                             reading=reading_record)

    # Get user's reading history
    readings = TarotReading.query.filter_by(user_id=current_user.id)\
                                .order_by(TarotReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/tarot.html', readings=readings)

@app.route('/tools/tarot/result/<int:reading_id>')
@login_required
def tarot_result(reading_id):
    """Display tarot reading result"""
    reading = TarotReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()
    return render_template('tools/tarot_result.html', reading=reading)

@app.route('/api/tarot/<int:reading_id>', methods=['DELETE'])
@login_required
def delete_tarot(reading_id):
    """Delete a tarot reading"""
    try:
        reading = TarotReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Lecture supprimée avec succès'})
    except Exception as e:
        print(f"Error deleting tarot reading: {e}")
        return jsonify({'success': False, 'message': 'Erreur lors de la suppression'}), 500

@app.route('/tools/tarot/download/<int:reading_id>')
@login_required
def download_tarot_pdf(reading_id):
    """Download tarot reading as PDF"""
    reading = TarotReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    try:
        # Generate PDF content
        pdf_content = generate_tarot_pdf(reading)

        # Create response
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="lecture_tarot_{reading.created_at.strftime("%Y%m%d_%H%M")}.pdf"'

        return response

    except Exception as e:
        print(f"Error generating tarot PDF: {e}")
        flash('Erreur lors de la génération du PDF', 'error')
        return redirect(url_for('tarot_result', reading_id=reading_id))

@app.route('/tools/palmistry', methods=['GET', 'POST'])
@login_required
def palmistry_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        hand_type = request.form['hand_type']
        question = request.form.get('question', '')

        # Handle file upload
        if 'palm_image' not in request.files:
            flash('Aucune image sélectionnée', 'error')
            return redirect(request.url)

        file = request.files['palm_image']
        if file.filename == '':
            flash('Aucune image sélectionnée', 'error')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            # Create uploads directory if it doesn't exist
            upload_dir = os.path.join(app.static_folder, 'uploads', 'palms')
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{current_user.id}_{filename}"
            filepath = os.path.join(upload_dir, filename)

            # Save file
            file.save(filepath)

            # Analyze palm with AI
            palm_analysis, interpretation, spiritual_advice, confidence = analyze_palm_with_ai(filepath, hand_type, question, client_name)

            # Save to database
            reading_record = PalmReading(
                user_id=current_user.id,
                client_name=client_name,
                hand_type=hand_type,
                image_filename=filename,
                question=question,
                palm_analysis=palm_analysis,
                interpretation=interpretation,
                spiritual_advice=spiritual_advice,
                confidence_score=confidence
            )
            db.session.add(reading_record)
            db.session.commit()

            return render_template('tools/palmistry_result.html', reading=reading_record)
        else:
            flash('Format de fichier non supporté', 'error')
            return redirect(request.url)

    # Get user's reading history
    readings = PalmReading.query.filter_by(user_id=current_user.id)\
                                .order_by(PalmReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/palmistry.html', readings=readings)

@app.route('/api/palm-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def palm_reading_api(reading_id):
    reading = PalmReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'hand_type': reading.hand_type,
            'image_filename': reading.image_filename,
            'question': reading.question,
            'palm_analysis': reading.palm_analysis,
            'interpretation': reading.interpretation,
            'spiritual_advice': reading.spiritual_advice,
            'confidence_score': reading.confidence_score,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        # Delete the image file
        if reading.image_filename:
            image_path = os.path.join(app.static_folder, 'uploads', 'palms', reading.image_filename)
            if os.path.exists(image_path):
                os.remove(image_path)

        # Delete the database record
        db.session.delete(reading)
        db.session.commit()

        return jsonify({'success': True})

@app.route('/download/palm-pdf/<int:reading_id>')
@login_required
def download_palm_pdf(reading_id):
    reading = PalmReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_palm_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="lecture_paume_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/runes', methods=['GET', 'POST'])
@login_required
def rune_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        reading_type = request.form['reading_type']
        question = request.form.get('question', '')

        # Generate rune reading
        runes_drawn, rune_analysis, interpretation, volva_guidance = generate_rune_reading(reading_type, question, client_name)

        # Save to database
        reading_record = RuneReading(
            user_id=current_user.id,
            client_name=client_name,
            reading_type=reading_type,
            question=question,
            runes_drawn=json.dumps(runes_drawn),
            rune_analysis=rune_analysis,
            interpretation=interpretation,
            volva_guidance=volva_guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        # Generate rune symbols for display
        rune_symbols = ''.join([rune['symbol'] for rune in runes_drawn])

        return render_template('tools/runes_result.html',
                             reading=reading_record,
                             rune_symbols=rune_symbols)

    # Get user's reading history
    readings = RuneReading.query.filter_by(user_id=current_user.id)\
                                .order_by(RuneReading.created_at.desc())\
                                .limit(10).all()

    return render_template('tools/runes.html', readings=readings)

@app.route('/api/rune-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def rune_reading_api(reading_id):
    reading = RuneReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        runes = json.loads(reading.runes_drawn) if reading.runes_drawn else []
        rune_symbols = ''.join([rune['symbol'] for rune in runes])

        reading_type_display = {
            'single': 'Rune Unique',
            'three_rune': 'Trois Nornes',
            'five_rune': 'Croix d\'Odin',
            'nine_rune': 'Arbre Yggdrasil'
        }.get(reading.reading_type, reading.reading_type)

        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'reading_type': reading.reading_type,
            'reading_type_display': reading_type_display,
            'question': reading.question,
            'runes_symbols': rune_symbols,
            'rune_analysis': reading.rune_analysis,
            'interpretation': reading.interpretation,
            'volva_guidance': reading.volva_guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/rune-pdf/<int:reading_id>')
@login_required
def download_rune_pdf(reading_id):
    reading = RuneReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_rune_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="consultation_runique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/kabbalah', methods=['GET', 'POST'])
@login_required
def kabbalah_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        full_name = request.form['full_name']
        birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date()

        # Generate kabbalistic reading
        hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance = generate_kabbalistic_reading(
            full_name, birth_date, client_name
        )

        # Save to database
        reading_record = KabbalisticReading(
            user_id=current_user.id,
            client_name=client_name,
            full_name=full_name,
            birth_date=birth_date,
            hebrew_calculation=hebrew_calculation,
            destiny_number=destiny_number,
            life_path_analysis=life_path_analysis,
            spiritual_guidance=spiritual_guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/kabbalah_result.html', reading=reading_record)

    # Get user's reading history
    readings = KabbalisticReading.query.filter_by(user_id=current_user.id)\
                                      .order_by(KabbalisticReading.created_at.desc())\
                                      .limit(10).all()

    return render_template('tools/kabbalah.html', readings=readings)

@app.route('/api/kabbalah-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def kabbalah_reading_api(reading_id):
    reading = KabbalisticReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'full_name': reading.full_name,
            'birth_date': reading.birth_date.strftime('%d/%m/%Y'),
            'hebrew_calculation': reading.hebrew_calculation,
            'destiny_number': reading.destiny_number,
            'life_path_analysis': reading.life_path_analysis,
            'spiritual_guidance': reading.spiritual_guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/kabbalah-pdf/<int:reading_id>')
@login_required
def download_kabbalah_pdf(reading_id):
    reading = KabbalisticReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_kabbalah_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="chemin_kabbalistique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/karmic', methods=['GET', 'POST'])
@login_required
def karmic_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date()
        birth_time = request.form['birth_time']
        birth_place = request.form['birth_place']

        # Generate karmic reading
        karmic_analysis, past_lives, karmic_blocks, soul_mission = generate_karmic_reading(
            birth_date, birth_time, birth_place, client_name
        )

        # Save to database
        reading_record = KarmicReading(
            user_id=current_user.id,
            client_name=client_name,
            birth_date=birth_date,
            birth_time=birth_time,
            birth_place=birth_place,
            karmic_analysis=karmic_analysis,
            past_lives=past_lives,
            karmic_blocks=karmic_blocks,
            soul_mission=soul_mission
        )
        db.session.add(reading_record)
        db.session.commit()

        return render_template('tools/karmic_result.html', reading=reading_record)

    # Get user's reading history
    readings = KarmicReading.query.filter_by(user_id=current_user.id)\
                                  .order_by(KarmicReading.created_at.desc())\
                                  .limit(10).all()

    return render_template('tools/karmic.html', readings=readings)

@app.route('/api/karmic-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def karmic_reading_api(reading_id):
    reading = KarmicReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'birth_date': reading.birth_date.strftime('%d/%m/%Y'),
            'birth_time': reading.birth_time,
            'birth_place': reading.birth_place,
            'karmic_analysis': reading.karmic_analysis,
            'past_lives': reading.past_lives,
            'karmic_blocks': reading.karmic_blocks,
            'soul_mission': reading.soul_mission,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/download/karmic-pdf/<int:reading_id>')
@login_required
def download_karmic_pdf(reading_id):
    reading = KarmicReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    # Generate PDF
    pdf_content = generate_karmic_pdf(reading)

    # Create response
    response = make_response(pdf_content)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename="analyse_karmique_{reading.client_name}_{reading.created_at.strftime("%Y%m%d")}.pdf"'

    return response

@app.route('/tools/pendulum', methods=['GET', 'POST'])
@login_required
def pendulum_reading():
    if request.method == 'POST':
        client_name = request.form['client_name']
        question = request.form['question']
        pendulum_response = request.form.get('pendulum_response', '')

        # Generate pendulum reading
        energy_reading, guidance = generate_pendulum_reading(question, pendulum_response, client_name)

        # Save to database
        reading_record = PendulumReading(
            user_id=current_user.id,
            client_name=client_name,
            question=question,
            pendulum_response=pendulum_response,
            energy_reading=energy_reading,
            guidance=guidance
        )
        db.session.add(reading_record)
        db.session.commit()

        # Return JSON for AJAX request
        return jsonify({
            'success': True,
            'energy_reading': energy_reading,
            'guidance': guidance,
            'pendulum_response': pendulum_response
        })

    # Get user's reading history
    readings = PendulumReading.query.filter_by(user_id=current_user.id)\
                                    .order_by(PendulumReading.created_at.desc())\
                                    .limit(10).all()

    return render_template('tools/pendulum.html', readings=readings)

@app.route('/api/pendulum-reading/<int:reading_id>', methods=['GET', 'DELETE'])
@login_required
def pendulum_reading_api(reading_id):
    reading = PendulumReading.query.filter_by(id=reading_id, user_id=current_user.id).first_or_404()

    if request.method == 'GET':
        return jsonify({
            'id': reading.id,
            'client_name': reading.client_name,
            'question': reading.question,
            'pendulum_response': reading.pendulum_response,
            'energy_reading': reading.energy_reading,
            'guidance': reading.guidance,
            'created_at': reading.created_at.strftime('%d/%m/%Y à %H:%M')
        })

    elif request.method == 'DELETE':
        db.session.delete(reading)
        db.session.commit()
        return jsonify({'success': True})

@app.route('/tools/horoscope', methods=['GET', 'POST'])
@login_required
def horoscope():
    if request.method == 'POST':
        birth_date = request.form['birth_date']
        birth_time = request.form.get('birth_time')
        birth_place = request.form['birth_place']
        current_date = request.form.get('current_date')
        prediction_months = int(request.form.get('prediction_months', 3))
        prediction_years = int(request.form.get('prediction_years', 0))

        # Update user's birth info
        current_user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        if birth_time:
            current_user.birth_time = datetime.strptime(birth_time, '%H:%M').time()
        current_user.birth_place = birth_place
        db.session.commit()

        # Generate horoscope with prediction periods
        horoscope_data = generate_horoscope(birth_date, birth_time, birth_place, current_date, prediction_months, prediction_years)

        # Store horoscope data in session for PDF download
        session['last_horoscope'] = horoscope_data

        return render_template('tools/horoscope_result.html',
                             horoscope=horoscope_data)

    return render_template('tools/horoscope.html')

@app.route('/tools/horoscope/download')
@login_required
def download_horoscope_pdf():
    """Download horoscope as PDF"""
    try:
        # Get horoscope data from session
        horoscope_data = session.get('last_horoscope')

        if not horoscope_data:
            flash('Veuillez d\'abord générer votre horoscope.', 'warning')
            return redirect(url_for('horoscope'))

        # Generate PDF
        pdf_content = generate_horoscope_pdf(horoscope_data, current_user)

        # Create response
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=horoscope_{current_user.username}_{datetime.now().strftime("%Y%m%d")}.pdf'

        return response

    except Exception as e:
        print(f"Error generating horoscope PDF: {e}")
        flash('Erreur lors de la génération du PDF.', 'error')
        return redirect(url_for('horoscope'))

@app.route('/tools/numerology', methods=['GET', 'POST'])
@login_required
def numerology_reading():
    if request.method == 'POST':
        full_name = request.form['full_name']
        birth_date = request.form['birth_date']

        # Generate numerology reading
        numerology_data = generate_numerology_reading(full_name, birth_date)

        # Store numerology data in a temporary file to avoid session size issues
        import tempfile
        import pickle

        # Create a temporary file with user ID in name
        temp_filename = f"numerology_{current_user.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.tmp"
        temp_path = os.path.join(tempfile.gettempdir(), temp_filename)

        # Save data to temporary file
        with open(temp_path, 'wb') as f:
            pickle.dump(numerology_data, f)

        # Store only the filename in session
        session['numerology_temp_file'] = temp_filename
        print(f"DEBUG: Stored numerology data in temp file: {temp_filename}")

        return render_template('tools/numerology_result.html',
                             numerology=numerology_data)

    return render_template('tools/numerology.html')

@app.route('/tools/numerology/download')
@login_required
def download_numerology_pdf():
    """Download numerology reading as PDF"""
    try:
        # Get the numerology data from temporary file
        import tempfile
        import pickle

        print(f"DEBUG: Session keys: {list(session.keys())}")
        if 'numerology_temp_file' not in session:
            print("DEBUG: numerology_temp_file not found in session")
            flash('Aucune lecture de numérologie trouvée. Veuillez d\'abord effectuer une analyse.', 'error')
            return redirect(url_for('numerology_reading'))

        temp_filename = session['numerology_temp_file']
        temp_path = os.path.join(tempfile.gettempdir(), temp_filename)

        # Check if temp file exists
        if not os.path.exists(temp_path):
            print(f"DEBUG: Temp file not found: {temp_path}")
            flash('Les données de numérologie ont expiré. Veuillez refaire une analyse.', 'error')
            return redirect(url_for('numerology_reading'))

        # Load data from temp file
        try:
            with open(temp_path, 'rb') as f:
                numerology_data = pickle.load(f)
            print(f"DEBUG: Retrieved numerology data from temp file: {len(str(numerology_data))} characters")
        except Exception as e:
            print(f"DEBUG: Error loading temp file: {e}")
            flash('Erreur lors de la lecture des données. Veuillez refaire une analyse.', 'error')
            return redirect(url_for('numerology_reading'))

        # Generate PDF
        pdf_content = generate_numerology_pdf(numerology_data)

        # Create response
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="numerologie_temple_du_voile_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

        # Clean up temporary file
        try:
            os.remove(temp_path)
            print(f"DEBUG: Cleaned up temp file: {temp_filename}")
        except:
            pass  # Ignore cleanup errors

        return response

    except Exception as e:
        print(f"Error generating numerology PDF: {e}")
        flash('Erreur lors de la génération du PDF.', 'error')
        return redirect(url_for('numerology_reading'))

# New pages routes
@app.route('/tarot')
def tarot():
    return render_template('tarot.html')

@app.route('/astrologie')
def astrologie():
    return render_template('astrologie.html')

@app.route('/numerologie')
def numerologie():
    return render_template('numerologie.html')

@app.route('/meditation')
def meditation():
    return render_template('meditation.html')

@app.route('/blog')
def blog():
    return render_template('blog.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

@app.route('/faq')
def faq():
    return render_template('faq.html')

@app.route('/aide')
def aide():
    return render_template('aide.html')

@app.route('/consultations')
def consultations():
    return render_template('consultations.html')

@app.route('/mentions-legales')
def mentions_legales():
    return render_template('mentions_legales.html')

@app.route('/politique-confidentialite')
def politique_confidentialite():
    return render_template('politique_confidentialite.html')

@app.route('/conditions-utilisation')
def conditions_utilisation():
    return render_template('conditions_utilisation.html')

@app.route('/cookies')
def cookies():
    return render_template('cookies.html')

@app.route('/formations')
def formations():
    return render_template('formations.html')

@app.route('/cours/introduction-sciences-occultes')
def cours_introduction_sciences_occultes():
    """Course: Introduction to Occult Sciences"""
    return render_template('courses/introduction_sciences_occultes.html')

def generate_dream_pdf(dream):
    """Generate PDF for dream interpretation"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import html

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=1*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7B1FA2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#D4AF37')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20,
            rightIndent=20
        )

        # Build PDF content
        story = []

        # Title
        story.append(Paragraph("🌙 INTERPRÉTATION DE RÊVE", title_style))
        story.append(Paragraph("Temple Du Voile - Analyse Experte", styles['Normal']))
        story.append(Spacer(1, 30))

        # Date
        story.append(Paragraph(f"<b>Date d'analyse :</b> {dream.created_at.strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Dream description
        story.append(Paragraph("💭 DESCRIPTION DU RÊVE", subtitle_style))
        story.append(Paragraph(html.escape(dream.dream_description), body_style))
        story.append(Spacer(1, 20))

        # Emotions if present
        if dream.emotions:
            story.append(Paragraph("💖 ÉMOTIONS RESSENTIES", subtitle_style))
            story.append(Paragraph(html.escape(dream.emotions), body_style))
            story.append(Spacer(1, 20))

        # Symbols if present
        if dream.symbols:
            story.append(Paragraph("⭐ SYMBOLES MARQUANTS", subtitle_style))
            story.append(Paragraph(html.escape(dream.symbols), body_style))
            story.append(Spacer(1, 20))

        # Interpretation
        story.append(Paragraph("🔮 ANALYSE EXPERTE", subtitle_style))

        # Clean and format interpretation text
        interpretation_text = dream.interpretation.replace('\n', '<br/>')
        interpretation_text = html.escape(interpretation_text).replace('&lt;br/&gt;', '<br/>')

        story.append(Paragraph(interpretation_text, body_style))
        story.append(Spacer(1, 30))

        # Footer
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )

        story.append(Paragraph("✨ Temple Du Voile - Votre Guide Spirituel ✨", footer_style))
        story.append(Paragraph("Analyse experte en symbolisme onirique et psychologie jungienne", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except ImportError:
        # Fallback if reportlab is not installed
        return generate_simple_dream_pdf(dream)
    except Exception as e:
        print(f"Error generating dream PDF: {e}")
        return generate_simple_dream_pdf(dream)

def generate_simple_dream_pdf(dream):
    """Generate simple text-based PDF fallback"""
    from io import BytesIO

    # Simple text content
    content = f"""
INTERPRÉTATION DE RÊVE - TEMPLE DU VOILE
========================================

Date d'analyse : {dream.created_at.strftime('%d/%m/%Y à %H:%M')}

DESCRIPTION DU RÊVE :
{dream.dream_description}

"""

    if dream.emotions:
        content += f"""
ÉMOTIONS RESSENTIES :
{dream.emotions}

"""

    if dream.symbols:
        content += f"""
SYMBOLES MARQUANTS :
{dream.symbols}

"""

    content += f"""
ANALYSE EXPERTE :
{dream.interpretation}

---
Temple Du Voile - Votre Guide Spirituel
Analyse experte en symbolisme onirique et psychologie jungienne
"""

    # Return as bytes for download
    return content.encode('utf-8')

def generate_tarot_pdf(reading):
    """Generate PDF for tarot reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import html

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=1*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7B1FA2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#D4AF37')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20,
            rightIndent=20
        )

        # Build PDF content
        story = []

        # Title
        story.append(Paragraph("🃏 LECTURE DE TAROT", title_style))
        story.append(Paragraph("Temple Du Voile - Consultation Experte", styles['Normal']))
        story.append(Spacer(1, 30))

        # Date and type
        reading_type_names = {
            'single_card': 'Carte Unique',
            'three_card': 'Tirage à Trois Cartes',
            'celtic_cross': 'Croix Celtique'
        }
        type_display = reading_type_names.get(reading.reading_type, reading.reading_type)

        story.append(Paragraph(f"<b>Date de consultation :</b> {reading.created_at.strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
        story.append(Paragraph(f"<b>Type de tirage :</b> {type_display}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Question
        story.append(Paragraph("❓ VOTRE QUESTION", subtitle_style))
        story.append(Paragraph(html.escape(reading.question), body_style))
        story.append(Spacer(1, 20))

        # Cards drawn
        story.append(Paragraph("🃏 CARTES TIRÉES", subtitle_style))

        try:
            cards = json.loads(reading.cards_drawn) if reading.cards_drawn else []
            for i, card in enumerate(cards, 1):
                card_info = f"<b>Carte {i} :</b> {html.escape(card.get('name', 'Carte inconnue'))}"
                if card.get('meaning'):
                    card_info += f" - <i>{html.escape(card['meaning'])}</i>"
                story.append(Paragraph(card_info, body_style))
        except:
            story.append(Paragraph("Cartes non disponibles", body_style))

        story.append(Spacer(1, 20))

        # Interpretation
        story.append(Paragraph("🔮 INTERPRÉTATION DES ARCANES", subtitle_style))

        # Clean and format interpretation text
        interpretation_text = reading.interpretation.replace('\n', '<br/>')
        interpretation_text = html.escape(interpretation_text).replace('&lt;br/&gt;', '<br/>')

        story.append(Paragraph(interpretation_text, body_style))
        story.append(Spacer(1, 30))

        # Footer
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )

        story.append(Paragraph("✨ Temple Du Voile - Votre Guide Spirituel ✨", footer_style))
        story.append(Paragraph("Consultation experte en tarologie et divination", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except ImportError:
        # Fallback if reportlab is not installed
        return generate_simple_tarot_pdf(reading)
    except Exception as e:
        print(f"Error generating tarot PDF: {e}")
        return generate_simple_tarot_pdf(reading)

def generate_simple_tarot_pdf(reading):
    """Generate simple text-based PDF fallback for tarot"""
    from io import BytesIO

    # Simple text content
    content = f"""
LECTURE DE TAROT - TEMPLE DU VOILE
==================================

Date de consultation : {reading.created_at.strftime('%d/%m/%Y à %H:%M')}
Type de tirage : {reading.reading_type}

VOTRE QUESTION :
{reading.question}

CARTES TIRÉES :
"""

    try:
        cards = json.loads(reading.cards_drawn) if reading.cards_drawn else []
        for i, card in enumerate(cards, 1):
            content += f"Carte {i} : {card.get('name', 'Carte inconnue')}"
            if card.get('meaning'):
                content += f" - {card['meaning']}"
            content += "\n"
    except:
        content += "Cartes non disponibles\n"

    content += f"""

INTERPRÉTATION DES ARCANES :
{reading.interpretation}

---
Temple Du Voile - Votre Guide Spirituel
Consultation experte en tarologie et divination
"""

    # Return as bytes for download
    return content.encode('utf-8')

def generate_dream_interpretation(description, emotions, symbols):
    """Generate dream interpretation using AI only"""
    try:
        # Create comprehensive prompt for AI dream interpretation
        prompt = f"""
        En tant qu'expert en interprétation des rêves et psychologie jungienne, analysez ce rêve avec profondeur et bienveillance.

        DESCRIPTION DU RÊVE:
        {description}

        ÉMOTIONS RESSENTIES:
        {emotions if emotions else "Non spécifiées"}

        SYMBOLES MARQUANTS:
        {symbols if symbols else "Non spécifiés"}

        Fournissez une interprétation complète qui:
        1. Analyse les symboles principaux et leur signification
        2. Explore les émotions et leur lien avec la vie éveillée
        3. Révèle les messages de l'inconscient
        4. Donne des conseils pratiques pour intégrer ces insights
        5. Utilise un ton mystique mais accessible

        Structurez votre réponse en 3-4 paragraphes d'environ 400-500 mots au total.
        Commencez par "Votre rêve révèle..." et utilisez un langage poétique et inspirant.
        Mentionnez spécifiquement les émotions et symboles fournis dans votre analyse.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un expert en interprétation des rêves, formé en psychologie jungienne et en symbolisme onirique. Votre style est mystique, bienveillant et profondément spirituel. Vous aidez les gens à comprendre les messages de leur inconscient."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=600,
            temperature=0.8
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error generating AI dream interpretation: {e}")
        # Return detailed error message for debugging
        error_details = str(e)
        if "api_key" in error_details.lower():
            return f"❌ **Erreur de configuration OpenAI** - La clé API n'est pas configurée correctement. Votre rêve sur '{description[:50]}...' nécessite une analyse par intelligence artificielle. Veuillez configurer votre clé OpenAI dans le fichier .env"
        elif "quota" in error_details.lower() or "billing" in error_details.lower():
            return f"❌ **Quota OpenAI dépassé** - Le quota d'utilisation de l'API OpenAI a été atteint. Votre rêve sur '{description[:50]}...' mérite une analyse approfondie. Veuillez vérifier votre compte OpenAI ou réessayer plus tard."
        else:
            return f"❌ **Erreur technique OpenAI** - Impossible de contacter l'API OpenAI ({error_details}). Votre rêve sur '{description[:50]}...' nécessite une analyse par intelligence artificielle. Veuillez réessayer dans quelques instants."

def get_all_tarot_cards():
    """Get complete tarot deck with image paths"""
    return [
        # Arcanes Majeurs
        {"id": "00", "name": "Le Mat", "meaning": "Nouveau départ, innocence, spontanéité", "image": "00-le-mat.jpg", "type": "major"},
        {"id": "01", "name": "Le Bateleur", "meaning": "Créativité, habileté, volonté", "image": "01-le-bateleur.jpg", "type": "major"},
        {"id": "02", "name": "La Papesse", "meaning": "Intuition, mystère, sagesse intérieure", "image": "02-la-papesse.jpg", "type": "major"},
        {"id": "03", "name": "L'Impératrice", "meaning": "Féminité, créativité, abondance", "image": "03-limpératrice.jpg", "type": "major"},
        {"id": "04", "name": "L'Empereur", "meaning": "Autorité, structure, contrôle", "image": "04-lempereur.jpg", "type": "major"},
        {"id": "05", "name": "Le Pape", "meaning": "Tradition, conformité, moralité", "image": "05-le-pape.jpg", "type": "major"},
        {"id": "06", "name": "L'Amoureux", "meaning": "Amour, choix, union", "image": "06-lamoureux.jpg", "type": "major"},
        {"id": "07", "name": "Le Chariot", "meaning": "Volonté, détermination, victoire", "image": "07-le-chariot.jpg", "type": "major"},
        {"id": "08", "name": "La Justice", "meaning": "Équité, vérité, cause et effet", "image": "08-la-justice.jpg", "type": "major"},
        {"id": "09", "name": "L'Hermite", "meaning": "Introspection, recherche, guidance intérieure", "image": "09-lhermite.jpg", "type": "major"},
        {"id": "10", "name": "La Roue de Fortune", "meaning": "Changement, cycles, destinée", "image": "10-la-roue-de-fortune.jpg", "type": "major"},
        {"id": "11", "name": "La Force", "meaning": "Force intérieure, courage, patience", "image": "11-la-force.jpg", "type": "major"},
        {"id": "12", "name": "Le Pendu", "meaning": "Sacrifice, attente, nouvelle perspective", "image": "12-le-pendu.jpg", "type": "major"},
        {"id": "13", "name": "L'Arcane sans Nom", "meaning": "Transformation, fin, renouveau", "image": "13-larcane-sans-nom.jpg", "type": "major"},
        {"id": "14", "name": "Tempérance", "meaning": "Modération, patience, but", "image": "14-tempérance.jpg", "type": "major"},
        {"id": "15", "name": "Le Diable", "meaning": "Bondage, addiction, sexualité", "image": "15-le-diable.jpg", "type": "major"},
        {"id": "16", "name": "La Maison Dieu", "meaning": "Révélation soudaine, bouleversement", "image": "16-la-maison-dieu.jpg", "type": "major"},
        {"id": "17", "name": "L'Étoile", "meaning": "Espoir, spiritualité, renouveau", "image": "17-létoile.jpg", "type": "major"},
        {"id": "18", "name": "La Lune", "meaning": "Illusion, peur, subconscient", "image": "18-la-lune.jpg", "type": "major"},
        {"id": "19", "name": "Le Soleil", "meaning": "Joie, succès, vitalité", "image": "19-le-soleil.jpg", "type": "major"},
        {"id": "20", "name": "Le Jugement", "meaning": "Jugement, renaissance, pardon", "image": "20-le-jugement.jpg", "type": "major"},
        {"id": "21", "name": "Le Monde", "meaning": "Accomplissement, voyage, succès", "image": "21-le-monde.jpg", "type": "major"},

        # Coupes
        {"id": "cups-01", "name": "As de Coupe", "meaning": "Nouveau départ émotionnel, amour", "image": "cups-01-as-de-coupe.jpg", "type": "cups"},
        {"id": "cups-02", "name": "Deux de Coupe", "meaning": "Partenariat, union, connexion", "image": "cups-02-deux-de-coupe.jpg", "type": "cups"},
        {"id": "cups-03", "name": "Trois de Coupe", "meaning": "Célébration, amitié, communauté", "image": "cups-03-trois-de-coupe.jpg", "type": "cups"},
        {"id": "cups-04", "name": "Quatre de Coupe", "meaning": "Apathie, contemplation, réévaluation", "image": "cups-04-quatre-de-coupe.jpg", "type": "cups"},
        {"id": "cups-05", "name": "Cinq de Coupe", "meaning": "Regret, perte, déception", "image": "cups-05-cinq-de-coupe.jpg", "type": "cups"},
        {"id": "cups-06", "name": "Six de Coupe", "meaning": "Nostalgie, enfance, innocence", "image": "cups-06-six-de-coupe.jpg", "type": "cups"},
        {"id": "cups-07", "name": "Sept de Coupe", "meaning": "Illusion, choix, rêverie", "image": "cups-07-sept-de-coupe.jpg", "type": "cups"},
        {"id": "cups-08", "name": "Huit de Coupe", "meaning": "Abandon, recherche spirituelle", "image": "cups-08-huit-de-coupe.jpg", "type": "cups"},
        {"id": "cups-09", "name": "Neuf de Coupe", "meaning": "Satisfaction, bonheur, accomplissement", "image": "cups-09-neuf-de-coupe.jpg", "type": "cups"},
        {"id": "cups-10", "name": "Dix de Coupe", "meaning": "Bonheur familial, harmonie", "image": "cups-10-dix-de-coupe.jpg", "type": "cups"},
        {"id": "cups-11", "name": "Valet de Coupe", "meaning": "Messager émotionnel, créativité", "image": "cups-11-valet-de-coupe.jpg", "type": "cups"},
        {"id": "cups-12", "name": "Cavalier de Coupe", "meaning": "Romance, charme, invitation", "image": "cups-12-cavalier-de-coupe.jpg", "type": "cups"},
        {"id": "cups-13", "name": "Reine de Coupe", "meaning": "Intuition, compassion, sécurité émotionnelle", "image": "cups-13-reine-de-coupe.jpg", "type": "cups"},
        {"id": "cups-14", "name": "Roi de Coupe", "meaning": "Maîtrise émotionnelle, diplomatie", "image": "cups-14-roi-de-coupe.jpg", "type": "cups"}
    ]

def generate_ai_tarot_reading(reading_type, question):
    """Generate complete tarot reading using AI - including card selection and interpretation"""
    try:
        # Determine number of cards
        num_cards = {
            'single': 1,
            'three_card': 3,
            'celtic_cross': 10
        }.get(reading_type, 1)

        # Create comprehensive prompt for AI to select cards and interpret
        prompt = f"""
        En tant que maître tarologue français expert, effectuez une lecture de tarot complète.

        ÉTAPE 1 - SÉLECTION DES CARTES:
        Choisissez {num_cards} carte(s) du tarot qui correspondent spirituellement à la situation.
        Question posée: {question if question else "Guidance générale"}
        Type de tirage: {reading_type}

        ÉTAPE 2 - INTERPRÉTATION COMPLÈTE:
        Fournissez une interprétation détaillée et spirituelle en TEXTE SIMPLE (pas de HTML).

        FORMAT DE RÉPONSE REQUIS:

        CARTES_SELECTIONNEES:
        [Listez chaque carte avec: Nom de la carte|Signification|Position dans le tirage]

        INTERPRETATION_COMPLETE:
        [Votre interprétation complète du tirage en texte simple, en analysant chaque carte et leurs interactions. Utilisez des paragraphes séparés par des sauts de ligne pour structurer votre réponse. N'utilisez AUCUNE balise HTML.]

        IMPORTANT: Répondez uniquement en texte simple, sans balises HTML, sans <p>, <br>, <strong>, etc.
        Utilisez un ton mystique et bienveillant, en français.
        Soyez précis dans la sélection des cartes - choisissez celles qui résonnent avec la question.
        Structurez votre interprétation en paragraphes clairs séparés par des sauts de ligne.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître tarologue français expert en divination et spiritualité. Vous possédez une connaissance approfondie du tarot de Marseille et des arcanes majeurs et mineurs. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.8
        )

        full_response = response.choices[0].message.content

        # Parse the AI response to extract cards and interpretation
        selected_cards = []
        interpretation = ""

        if "CARTES_SELECTIONNEES:" in full_response:
            sections = full_response.split("CARTES_SELECTIONNEES:")
            if len(sections) > 1:
                cards_section = sections[1].split("INTERPRETATION_COMPLETE:")[0].strip()

                # Parse cards from AI response
                card_lines = [line.strip() for line in cards_section.split('\n') if line.strip() and '|' in line]
                for i, line in enumerate(card_lines):
                    if i < num_cards:
                        parts = line.split('|')
                        if len(parts) >= 2:
                            card = {
                                'id': f"expert-{i+1}",
                                'name': parts[0].strip(),
                                'meaning': parts[1].strip() if len(parts) > 1 else "Guidance spirituelle",
                                'position': parts[2].strip() if len(parts) > 2 else f"Position {i+1}",
                                'image': "card-back.jpg"  # Use generic back since we don't have specific images
                            }
                            selected_cards.append(card)

        # Extract interpretation
        if "INTERPRETATION_COMPLETE:" in full_response:
            interpretation = full_response.split("INTERPRETATION_COMPLETE:")[1].strip()

        # Fallback if parsing failed
        if not selected_cards:
            # Use fallback cards from database
            tarot_cards = get_all_tarot_cards()
            selected_cards = random.sample(tarot_cards, num_cards)

        if not interpretation:
            interpretation = "Cette lecture révèle des énergies importantes pour votre chemin spirituel. Les cartes vous invitent à la réflexion et à l'action consciente."

        return selected_cards, interpretation

    except Exception as e:
        print(f"Error in AI tarot reading generation: {e}")
        # Fallback to database cards
        tarot_cards = get_all_tarot_cards()
        selected_cards = random.sample(tarot_cards, num_cards)
        interpretation = "Cette lecture révèle des énergies importantes pour votre chemin spirituel."
        return selected_cards, interpretation

def generate_random_cards(reading_type):
    """Generate random cards for fallback"""
    tarot_cards = get_all_tarot_cards()

    if reading_type == 'single':
        return [random.choice(tarot_cards)]
    elif reading_type == 'three_card':
        return random.sample(tarot_cards, 3)
    else:  # celtic_cross
        return random.sample(tarot_cards, 10)

def generate_ai_tarot_interpretation(cards, question, reading_type):
    """Generate AI-powered tarot interpretation using OpenAI"""
    try:
        # Prepare the prompt for OpenAI
        cards_description = ""
        for i, card in enumerate(cards):
            position_name = get_position_name(i, reading_type)
            cards_description += f"{position_name}: {card['name']} - {card['meaning']}\n"

        prompt = f"""
        En tant qu'expert en tarot français, interprétez ce tirage de tarot avec sagesse et bienveillance.

        Question posée: {question}
        Type de tirage: {reading_type}

        Cartes tirées:
        {cards_description}

        Fournissez une interprétation détaillée et personnalisée qui:
        1. Répond à la question posée
        2. Explique la signification de chaque carte dans sa position
        3. Donne des conseils pratiques et spirituels
        4. Utilise un ton mystique mais accessible
        5. Fait environ 300-400 mots

        IMPORTANT: Répondez uniquement en TEXTE SIMPLE, sans balises HTML.
        N'utilisez AUCUNE balise comme <p>, <br>, <strong>, <em>, etc.
        Structurez votre réponse en paragraphes séparés par des sauts de ligne.

        Commencez par "Votre tirage révèle..." et utilisez un langage poétique et inspirant.
        """

        # Call OpenAI API with GPT-4o
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître tarologue français expert en divination et spiritualité. Vous possédez une connaissance approfondie du tarot de Marseille et des arcanes majeurs et mineurs. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=600,
            temperature=0.8
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error generating AI interpretation: {e}")
        # Return error message instead of mock data
        return f"Je ne peux pas interpréter votre tirage en ce moment en raison d'un problème technique. Votre question '{question}' mérite une analyse approfondie. Veuillez réessayer dans quelques instants pour recevoir une interprétation personnalisée de vos cartes."

def get_position_name(index, reading_type):
    """Get the name of the position based on reading type"""
    if reading_type == 'single':
        return "Carte unique"
    elif reading_type == 'three_card':
        positions = ["Passé", "Présent", "Futur"]
        return positions[index] if index < len(positions) else f"Position {index + 1}"
    else:  # celtic_cross
        positions = [
            "Situation actuelle", "Défi/Obstacle", "Passé lointain", "Futur possible",
            "Couronne/But", "Fondation", "Votre approche", "Influences extérieures",
            "Espoirs et peurs", "Résultat final"
        ]
        return positions[index] if index < len(positions) else f"Position {index + 1}"

# Removed basic interpretation function - now using only AI-generated content

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_palm_with_ai(image_path, hand_type, question, client_name):
    """Analyze palm using GPT-4o Vision"""
    try:
        import base64

        # Read and encode image
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Prepare prompt for palm reading
        prompt = f"""
        Analysez cette image de paume avec expertise en chiromancie pour {client_name}.

        INFORMATIONS:
        - Type de main: {hand_type}
        - Question: {question if question else "Lecture générale"}

        INSTRUCTIONS D'ANALYSE:
        Examinez attentivement cette image de paume et fournissez une analyse détaillée en 3 sections:

        SECTION 1 - ANALYSE DES LIGNES:
        Décrivez ce que vous observez dans les lignes principales:
        - Ligne de vie: longueur, profondeur, interruptions
        - Ligne de tête: direction, clarté, bifurcations
        - Ligne de cœur: position, forme, caractéristiques
        - Ligne de destinée: présence, direction, intensité
        - Forme générale de la main et des doigts
        - Monts palmaires visibles

        SECTION 2 - INTERPRÉTATION SPIRITUELLE:
        Basé sur votre observation des lignes:
        - Traits de personnalité révélés
        - Potentiels et talents naturels
        - Défis et opportunités de croissance
        - Réponse à la question posée

        SECTION 3 - CONSEILS SPIRITUELS:
        Guidance personnalisée:
        - Conseils pour développer les potentiels
        - Pratiques spirituelles recommandées
        - Affirmations positives
        - Guidance pour l'évolution personnelle

        IMPORTANT: Analysez réellement l'image fournie. Décrivez ce que vous voyez concrètement dans les lignes de cette paume spécifique. Utilisez un ton mystique et bienveillant en français.
        """

        # Call OpenAI API with vision
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        # Determine image format from file extension
        image_format = "jpeg"
        if image_path.lower().endswith(('.png', '.webp')):
            image_format = image_path.split('.')[-1].lower()

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "Vous êtes un maître chiromancien français expert en lecture de la paume. Vous analysez les images de paumes avec précision et fournissez des interprétations spirituelles authentiques. Votre style est mystique, précis et bienveillant. Vous décrivez toujours ce que vous observez réellement dans l'image."
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{image_format};base64,{base64_image}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1200,
            temperature=0.7
        )

        full_analysis = response.choices[0].message.content

        # Split analysis into three sections
        palm_analysis = ""
        interpretation = ""
        spiritual_advice = ""

        if "SECTION 1" in full_analysis and "SECTION 2" in full_analysis and "SECTION 3" in full_analysis:
            # Split by sections
            sections = full_analysis.split("SECTION")
            if len(sections) >= 4:
                palm_analysis = "SECTION" + sections[1].strip()
                interpretation = "SECTION" + sections[2].strip()
                spiritual_advice = "SECTION" + sections[3].strip()
            else:
                # Fallback if sections are not properly formatted
                palm_analysis = full_analysis[:len(full_analysis)//3]
                interpretation = full_analysis[len(full_analysis)//3:2*len(full_analysis)//3]
                spiritual_advice = full_analysis[2*len(full_analysis)//3:]
        else:
            # Fallback split for older format
            if "INTERPRÉTATION SPIRITUELLE" in full_analysis:
                parts = full_analysis.split("INTERPRÉTATION SPIRITUELLE")
                palm_analysis = parts[0].strip()
                remaining = "INTERPRÉTATION SPIRITUELLE" + parts[1].strip()

                if "CONSEILS" in remaining:
                    interp_parts = remaining.split("CONSEILS")
                    interpretation = interp_parts[0].strip()
                    spiritual_advice = "CONSEILS" + interp_parts[1].strip()
                else:
                    interpretation = remaining
                    spiritual_advice = "Méditez sur les révélations de votre paume et laissez votre intuition vous guider."
            else:
                # Basic fallback
                lines = full_analysis.split('\n')
                third = len(lines) // 3
                palm_analysis = '\n'.join(lines[:third])
                interpretation = '\n'.join(lines[third:2*third])
                spiritual_advice = '\n'.join(lines[2*third:])

        # Generate confidence score based on response quality
        confidence = min(0.95, len(full_analysis) / 1200.0 + 0.6)

        return palm_analysis, interpretation, spiritual_advice, confidence

    except Exception as e:
        error_msg = str(e)
        print(f"Error analyzing palm with AI: {error_msg}")

        # Check if it's an API error or image processing error
        if "content_policy_violation" in error_msg.lower():
            error_analysis = "L'image fournie ne peut pas être analysée en raison des politiques de contenu. Veuillez fournir une image claire de votre paume."
        elif "invalid_image" in error_msg.lower() or "image" in error_msg.lower():
            error_analysis = "L'image fournie n'est pas dans un format valide ou n'est pas suffisamment claire pour l'analyse. Veuillez télécharger une image de meilleure qualité."
        else:
            # Return comprehensive fallback analysis
            error_analysis = f"""ANALYSE TECHNIQUE DES LIGNES:

Votre main {hand_type} révèle des caractéristiques importantes pour votre développement spirituel. Les lignes principales de votre paume indiquent un potentiel remarquable dans plusieurs domaines de votre vie.

La ligne de vie suggère une vitalité naturelle et une capacité d'adaptation face aux défis. Votre ligne de tête révèle une intelligence pratique et une approche réfléchie des situations. La ligne de cœur indique une nature émotionnelle riche et des relations profondes."""

        error_interpretation = f"""INTERPRÉTATION SPIRITUELLE:

Votre paume révèle une personnalité équilibrée avec un fort potentiel de croissance spirituelle. Les énergies présentes dans votre main indiquent que vous êtes à un moment important de votre évolution personnelle.

Concernant votre question "{question if question else 'guidance générale'}", les lignes de votre main suggèrent que la réponse se trouve dans l'équilibre entre votre intuition et votre sagesse pratique. Votre chemin spirituel vous guide vers une meilleure compréhension de votre véritable nature."""

        error_advice = f"""CONSEILS SPIRITUELS PERSONNALISÉS:

• Méditez quotidiennement sur les révélations de votre paume
• Observez attentivement les changements dans votre vie
• Gardez l'esprit ouvert aux nouvelles opportunités
• Faites confiance à votre intuition naturelle
• Prenez soin de votre santé physique et mentale
• Pratiquez la gratitude pour attirer l'abondance

La sagesse de votre main vous encourage à poursuivre votre quête spirituelle avec confiance et détermination."""

        return error_analysis, error_interpretation, error_advice, 0.75

# Removed basic palm analysis function - now using only AI-generated content

def get_rune_database():
    """Get complete rune database with Elder Futhark"""
    return [
        {"symbol": "ᚠ", "name": "Fehu", "meaning": "Richesse, bétail, prospérité matérielle", "element": "Feu", "keywords": ["abondance", "succès", "énergie créatrice"]},
        {"symbol": "ᚢ", "name": "Uruz", "meaning": "Force primitive, taureau sauvage, vitalité", "element": "Terre", "keywords": ["force", "santé", "courage"]},
        {"symbol": "ᚦ", "name": "Thurisaz", "meaning": "Géant, épine, protection", "element": "Feu", "keywords": ["défense", "conflit", "transformation"]},
        {"symbol": "ᚨ", "name": "Ansuz", "meaning": "Dieu, souffle divin, communication", "element": "Air", "keywords": ["sagesse", "inspiration", "révélation"]},
        {"symbol": "ᚱ", "name": "Raidho", "meaning": "Voyage, chevauchée, mouvement", "element": "Air", "keywords": ["voyage", "progression", "rythme"]},
        {"symbol": "ᚲ", "name": "Kenaz", "meaning": "Torche, connaissance, illumination", "element": "Feu", "keywords": ["créativité", "inspiration", "transformation"]},
        {"symbol": "ᚷ", "name": "Gebo", "meaning": "Don, échange, partenariat", "element": "Air", "keywords": ["générosité", "équilibre", "relation"]},
        {"symbol": "ᚹ", "name": "Wunjo", "meaning": "Joie, bonheur, harmonie", "element": "Terre", "keywords": ["bonheur", "succès", "accomplissement"]},
        {"symbol": "ᚺ", "name": "Hagalaz", "meaning": "Grêle, destruction créatrice", "element": "Eau", "keywords": ["changement", "crise", "libération"]},
        {"symbol": "ᚾ", "name": "Nauthiz", "meaning": "Besoin, nécessité, contrainte", "element": "Feu", "keywords": ["patience", "endurance", "leçon"]},
        {"symbol": "ᛁ", "name": "Isa", "meaning": "Glace, stagnation, concentration", "element": "Eau", "keywords": ["patience", "introspection", "clarté"]},
        {"symbol": "ᛃ", "name": "Jera", "meaning": "Année, cycle, récolte", "element": "Terre", "keywords": ["patience", "récompense", "cycles naturels"]},
        {"symbol": "ᛇ", "name": "Eihwaz", "meaning": "If, endurance, protection", "element": "Terre", "keywords": ["protection", "endurance", "initiation"]},
        {"symbol": "ᛈ", "name": "Perthro", "meaning": "Dés, destin, mystère", "element": "Eau", "keywords": ["destin", "mystère", "révélation"]},
        {"symbol": "ᛉ", "name": "Algiz", "meaning": "Élan, protection, connexion divine", "element": "Air", "keywords": ["protection", "spiritualité", "guidance"]},
        {"symbol": "ᛊ", "name": "Sowilo", "meaning": "Soleil, victoire, énergie", "element": "Feu", "keywords": ["succès", "victoire", "énergie positive"]},
        {"symbol": "ᛏ", "name": "Tiwaz", "meaning": "Tyr, justice, sacrifice", "element": "Air", "keywords": ["justice", "courage", "leadership"]},
        {"symbol": "ᛒ", "name": "Berkano", "meaning": "Bouleau, croissance, féminité", "element": "Terre", "keywords": ["croissance", "fertilité", "nouveau départ"]},
        {"symbol": "ᛖ", "name": "Ehwaz", "meaning": "Cheval, partenariat, loyauté", "element": "Terre", "keywords": ["partenariat", "confiance", "progrès"]},
        {"symbol": "ᛗ", "name": "Mannaz", "meaning": "Homme, humanité, soi", "element": "Air", "keywords": ["humanité", "coopération", "intelligence"]},
        {"symbol": "ᛚ", "name": "Laguz", "meaning": "Eau, intuition, flux", "element": "Eau", "keywords": ["intuition", "émotions", "flux de vie"]},
        {"symbol": "ᛜ", "name": "Ingwaz", "meaning": "Ing, fertilité, potentiel", "element": "Terre", "keywords": ["fertilité", "potentiel", "gestation"]},
        {"symbol": "ᛞ", "name": "Dagaz", "meaning": "Jour, éveil, transformation", "element": "Feu", "keywords": ["éveil", "transformation", "nouveau jour"]},
        {"symbol": "ᛟ", "name": "Othala", "meaning": "Héritage, foyer, tradition", "element": "Terre", "keywords": ["héritage", "famille", "tradition"]}
    ]

def generate_rune_reading(reading_type, question, client_name):
    """Generate rune reading with AI interpretation"""
    # Generate complete rune reading using AI
    try:
        selected_runes, rune_analysis, interpretation, volva_guidance = generate_ai_rune_reading_complete(
            reading_type, question, client_name
        )
        return selected_runes, rune_analysis, interpretation, volva_guidance
    except Exception as e:
        print(f"Error generating AI rune reading: {e}")
        # If AI fails, return error message
        return [], "Erreur lors de la génération de la lecture runique", "Veuillez réessayer plus tard", "Les runes ne peuvent pas être consultées en ce moment"

def generate_ai_rune_reading_complete(reading_type, question, client_name):
    """Generate complete rune reading using AI - including rune selection and interpretation"""
    try:
        # Determine number of runes and positions
        num_runes = {
            'single': 1,
            'three_rune': 3,
            'five_rune': 5,
            'nine_rune': 9
        }.get(reading_type, 1)

        positions = get_rune_positions(reading_type)

        # Create comprehensive prompt for AI to select runes and interpret
        prompt = f"""
        En tant que Völva experte en divination runique nordique, effectuez une consultation complète pour {client_name}.

        ÉTAPE 1 - SÉLECTION DES RUNES:
        Choisissez {num_runes} rune(s) du Futhark Elder qui correspondent spirituellement à la situation de {client_name}.
        Question posée: {question if question else "Guidance générale"}
        Type de tirage: {reading_type}
        Positions: {', '.join(positions)}

        ÉTAPE 2 - ANALYSE ET INTERPRÉTATION:
        Fournissez une analyse complète structurée.

        FORMAT DE RÉPONSE REQUIS:

        RUNES_SELECTED:
        [Listez chaque rune sélectionnée avec: Symbole runique|Nom|Signification|Position]

        ANALYSE_RUNES:
        [Analyse détaillée des runes et de leurs interactions dans leurs positions]

        INTERPRETATION_SPIRITUELLE:
        [Interprétation spirituelle complète pour répondre à la question]

        GUIDANCE_VOLVA:
        [Conseils pratiques et guidance spirituelle basés sur la sagesse nordique]

        Utilisez un ton mystique et authentique, en français, avec des références à la mythologie nordique.
        Soyez précis dans la sélection des runes - choisissez celles qui résonnent vraiment avec la situation.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes une Völva, une voyante nordique experte en divination runique. Vous possédez une connaissance approfondie des 24 runes Elder Futhark et de leur signification spirituelle. Vous sélectionnez intuitivement les runes appropriées pour chaque consultation et fournissez des interprétations authentiques basées sur la tradition nordique."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.8
        )

        full_response = response.choices[0].message.content

        # Parse the AI response
        selected_runes = []
        rune_analysis = ""
        interpretation = ""
        volva_guidance = ""

        # Extract runes section
        if "RUNES_SELECTED:" in full_response:
            sections = full_response.split("RUNES_SELECTED:")
            if len(sections) > 1:
                runes_section = sections[1].split("ANALYSE_RUNES:")[0].strip()

                # Parse runes from AI response
                rune_lines = [line.strip() for line in runes_section.split('\n') if line.strip() and '|' in line]
                for i, line in enumerate(rune_lines):
                    if i < num_runes:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            rune = {
                                'symbol': parts[0].strip(),
                                'name': parts[1].strip(),
                                'meaning': parts[2].strip(),
                                'position': parts[3].strip(),
                                'keywords': [parts[2].strip()]
                            }
                            selected_runes.append(rune)

        # If AI didn't provide proper runes, use fallback
        if len(selected_runes) < num_runes:
            rune_database = get_rune_database()
            selected_runes = random.sample(rune_database, num_runes)
            for i, rune in enumerate(selected_runes):
                rune['position'] = positions[i] if i < len(positions) else f"Position {i+1}"

        # Extract analysis section
        if "ANALYSE_RUNES:" in full_response:
            analysis_section = full_response.split("ANALYSE_RUNES:")[1]
            if "INTERPRETATION_SPIRITUELLE:" in analysis_section:
                rune_analysis = analysis_section.split("INTERPRETATION_SPIRITUELLE:")[0].strip()
            else:
                rune_analysis = analysis_section.strip()

        # Extract interpretation section
        if "INTERPRETATION_SPIRITUELLE:" in full_response:
            interp_section = full_response.split("INTERPRETATION_SPIRITUELLE:")[1]
            if "GUIDANCE_VOLVA:" in interp_section:
                interpretation = interp_section.split("GUIDANCE_VOLVA:")[0].strip()
            else:
                interpretation = interp_section.strip()

        # Extract guidance section
        if "GUIDANCE_VOLVA:" in full_response:
            volva_guidance = full_response.split("GUIDANCE_VOLVA:")[1].strip()

        # Ensure we have content for all sections
        if not rune_analysis:
            rune_analysis = "Les runes sélectionnées révèlent des énergies importantes pour votre chemin spirituel."
        if not interpretation:
            interpretation = "Cette consultation runique apporte guidance et sagesse pour votre situation actuelle."
        if not volva_guidance:
            volva_guidance = "Méditez sur le message des runes et laissez leur sagesse vous guider."

        return selected_runes, rune_analysis, interpretation, volva_guidance

    except Exception as e:
        print(f"Error in AI rune reading generation: {e}")
        raise e

# Removed old functions - now using only AI-generated content

def get_rune_positions(reading_type):
    """Get position names for different reading types"""
    positions = {
        'single': ["Guidance Présente"],
        'three_rune': ["Passé (Urd)", "Présent (Verdandi)", "Futur (Skuld)"],
        'five_rune': ["Situation", "Défi", "Passé", "Futur", "Résultat"],
        'nine_rune': ["Asgard", "Alfheim", "Vanaheim", "Midgard", "Jotunheim", "Muspelheim", "Svartalfheim", "Helheim", "Niflheim"]
    }
    return positions.get(reading_type, [f"Position {i+1}" for i in range(9)])

def generate_palm_pdf(reading):
    """Generate PDF report for palm reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Lecture de Chiromancie", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Type de main:', reading.hand_type.title()],
            ['Date de lecture:', reading.created_at.strftime('%d/%m/%Y à %H:%M')],
            ['Score de confiance:', f"{reading.confidence_score*100:.0f}%" if reading.confidence_score else 'N/A']
        ]

        if reading.question:
            client_data.append(['Question posée:', reading.question])

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            # Remove HTML tags
            clean = re.sub('<.*?>', '', text)
            # Replace HTML entities
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Analysis section
        if reading.palm_analysis:
            story.append(Paragraph("ANALYSE DES LIGNES DE LA MAIN", subtitle_style))
            analysis_text = clean_html(reading.palm_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Interpretation section
        if reading.interpretation:
            story.append(Paragraph("INTERPRÉTATION SPIRITUELLE", subtitle_style))
            interpretation_text = clean_html(reading.interpretation)
            story.append(Paragraph(interpretation_text, body_style))
            story.append(Spacer(1, 20))

        # Spiritual advice section
        if reading.spiritual_advice:
            story.append(Paragraph("CONSEILS SPIRITUELS PERSONNALISÉS", subtitle_style))
            advice_text = clean_html(reading.spiritual_advice)
            story.append(Paragraph(advice_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Votre guide spirituel", footer_style))
        story.append(Paragraph("Cette lecture est à des fins de divertissement et de développement personnel", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating PDF: {e}")
        # Return a simple error PDF
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF")
        return buffer.getvalue()

def generate_rune_pdf(reading):
    """Generate PDF report for rune reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Consultation Runique Nordique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        reading_type_display = {
            'single': 'Rune Unique',
            'three_rune': 'Trois Nornes',
            'five_rune': 'Croix d\'Odin',
            'nine_rune': 'Arbre Yggdrasil'
        }.get(reading.reading_type, reading.reading_type)

        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Type de tirage:', reading_type_display],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        if reading.question:
            client_data.append(['Question aux Nornes:', reading.question])

        # Add runes drawn
        if reading.runes_drawn:
            runes = json.loads(reading.runes_drawn)
            rune_symbols = ' '.join([rune['symbol'] for rune in runes])
            rune_names = ', '.join([rune['name'] for rune in runes])
            client_data.append(['Runes tirées:', f"{rune_symbols} ({rune_names})"])

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Analysis section
        if reading.rune_analysis:
            story.append(Paragraph("ANALYSE DES RUNES SACRÉES", subtitle_style))
            analysis_text = clean_html(reading.rune_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Interpretation section
        if reading.interpretation:
            story.append(Paragraph("INTERPRÉTATION SPIRITUELLE", subtitle_style))
            interpretation_text = clean_html(reading.interpretation)
            story.append(Paragraph(interpretation_text, body_style))
            story.append(Spacer(1, 20))

        # Völva guidance section
        if reading.volva_guidance:
            story.append(Paragraph("GUIDANCE DE LA VÖLVA", subtitle_style))
            guidance_text = clean_html(reading.volva_guidance)
            story.append(Paragraph(guidance_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Nordique Ancestrale", footer_style))
        story.append(Paragraph("Que les runes vous guident sur le chemin de la sagesse", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating rune PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF runique")
        return buffer.getvalue()

def generate_kabbalah_pdf(reading):
    """Generate PDF report for kabbalistic reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#d4af37')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Chemin de Vie Kabbalistique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Nom complet:', reading.full_name],
            ['Date de naissance:', reading.birth_date.strftime('%d/%m/%Y')],
            ['Nombre de destinée:', str(reading.destiny_number)],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Hebrew calculation section
        if reading.hebrew_calculation:
            story.append(Paragraph("CALCUL HÉBRAÏQUE", subtitle_style))
            calculation_text = clean_html(reading.hebrew_calculation)
            story.append(Paragraph(calculation_text, body_style))
            story.append(Spacer(1, 20))

        # Life path analysis section
        if reading.life_path_analysis:
            story.append(Paragraph("ANALYSE DU CHEMIN DE VIE", subtitle_style))
            analysis_text = clean_html(reading.life_path_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Spiritual guidance section
        if reading.spiritual_guidance:
            story.append(Paragraph("GUIDANCE SPIRITUELLE", subtitle_style))
            guidance_text = clean_html(reading.spiritual_guidance)
            story.append(Paragraph(guidance_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Kabbalistique", footer_style))
        story.append(Paragraph("Que la lumière divine illumine votre chemin", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating kabbalah PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF kabbalistique")
        return buffer.getvalue()

def generate_karmic_pdf(reading):
    """Generate PDF report for karmic reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import re

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7b1fa2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#8b4513')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20
        )

        # Build PDF content
        story = []

        # Header
        story.append(Paragraph("🔮 TEMPLE DU VOILE 🔮", title_style))
        story.append(Paragraph("Analyse Karmique Védique", subtitle_style))
        story.append(Spacer(1, 20))

        # Client info table
        client_data = [
            ['Client:', reading.client_name or 'Non spécifié'],
            ['Date de naissance:', reading.birth_date.strftime('%d/%m/%Y')],
            ['Heure de naissance:', reading.birth_time],
            ['Lieu de naissance:', reading.birth_place],
            ['Date de consultation:', reading.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(client_table)
        story.append(Spacer(1, 30))

        # Clean HTML tags from text
        def clean_html(text):
            if not text:
                return ""
            clean = re.sub('<.*?>', '', text)
            clean = clean.replace('&nbsp;', ' ').replace('&amp;', '&')
            return clean.strip()

        # Karmic analysis section
        if reading.karmic_analysis:
            story.append(Paragraph("ANALYSE KARMIQUE", subtitle_style))
            analysis_text = clean_html(reading.karmic_analysis)
            story.append(Paragraph(analysis_text, body_style))
            story.append(Spacer(1, 20))

        # Past lives section
        if reading.past_lives:
            story.append(Paragraph("VIES PASSÉES", subtitle_style))
            past_lives_text = clean_html(reading.past_lives)
            story.append(Paragraph(past_lives_text, body_style))
            story.append(Spacer(1, 20))

        # Karmic blocks section
        if reading.karmic_blocks:
            story.append(Paragraph("BLOCAGES KARMIQUES", subtitle_style))
            blocks_text = clean_html(reading.karmic_blocks)
            story.append(Paragraph(blocks_text, body_style))
            story.append(Spacer(1, 20))

        # Soul mission section
        if reading.soul_mission:
            story.append(Paragraph("MISSION D'ÂME", subtitle_style))
            mission_text = clean_html(reading.soul_mission)
            story.append(Paragraph(mission_text, body_style))
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("Temple Du Voile - Sagesse Karmique Védique", footer_style))
        story.append(Paragraph("Que votre âme trouve la paix et l'évolution", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except Exception as e:
        print(f"Error generating karmic PDF: {e}")
        buffer = BytesIO()
        buffer.write(b"Erreur lors de la generation du PDF karmique")
        return buffer.getvalue()

def generate_kabbalistic_reading(full_name, birth_date, client_name):
    """Generate kabbalistic life path reading using AI"""
    try:
        # Calculate Hebrew letter values
        hebrew_calculation = calculate_hebrew_values(full_name)
        destiny_number = calculate_destiny_number(full_name)

        # Create comprehensive prompt for AI kabbalistic interpretation
        prompt = f"""
        En tant qu'expert en Kabbale et numérologie hébraïque, analysez le chemin de vie kabbalistique pour {client_name}.

        DONNÉES KABBALISTIQUES:
        Nom complet: {full_name}
        Date de naissance: {birth_date.strftime('%d/%m/%Y')}
        Calcul hébraïque: {hebrew_calculation}
        Nombre de destinée: {destiny_number}

        Fournissez une analyse complète structurée en 3 sections:

        SECTION 1 - ANALYSE DU CHEMIN DE VIE:
        - Signification spirituelle du nombre de destinée {destiny_number}
        - Interprétation des lettres hébraïques du nom
        - Mission spirituelle révélée par la Kabbale
        - Talents et dons cachés
        - Défis karmiques à surmonter

        SECTION 2 - GUIDANCE SPIRITUELLE:
        - Conseils pour accomplir sa mission
        - Pratiques spirituelles recommandées
        - Périodes favorables dans la vie
        - Relations et partenariats spirituels
        - Développement de la conscience

        Utilisez un ton mystique et respectueux de la tradition kabbalistique.
        Référencez l'Arbre de Vie, les Sephiroth et la sagesse hébraïque.
        Environ 500-600 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître kabbaliste expert en numérologie hébraïque et en spiritualité juive. Vous possédez une connaissance approfondie de l'Arbre de Vie, des Sephiroth et de la signification mystique des lettres hébraïques. Votre style est respectueux, mystique et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=700,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        life_path_analysis = ""
        spiritual_guidance = ""

        if "SECTION 1" in full_interpretation and "SECTION 2" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 3:
                life_path_analysis = "SECTION" + sections[1].strip()
                spiritual_guidance = "SECTION" + sections[2].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                half = len(lines) // 2
                life_path_analysis = '\n'.join(lines[:half])
                spiritual_guidance = '\n'.join(lines[half:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            half = len(lines) // 2
            life_path_analysis = '\n'.join(lines[:half])
            spiritual_guidance = '\n'.join(lines[half:])

        return hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance

    except Exception as e:
        print(f"Error generating kabbalistic reading: {e}")
        # Fallback
        hebrew_calculation = f"Calcul hébraïque pour {full_name}"
        destiny_number = calculate_destiny_number(full_name)
        life_path_analysis = f"Votre nombre de destinée {destiny_number} révèle un chemin spirituel unique."
        spiritual_guidance = "Méditez sur la sagesse de vos ancêtres et suivez votre intuition."
        return hebrew_calculation, destiny_number, life_path_analysis, spiritual_guidance

def calculate_hebrew_values(name):
    """Calculate Hebrew letter values for a name"""
    # Hebrew letter values (simplified mapping)
    hebrew_values = {
        'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8, 'I': 9,
        'J': 10, 'K': 20, 'L': 30, 'M': 40, 'N': 50, 'O': 60, 'P': 70, 'Q': 80,
        'R': 90, 'S': 100, 'T': 200, 'U': 300, 'V': 400, 'W': 500, 'X': 600,
        'Y': 700, 'Z': 800
    }

    calculation = ""
    total = 0

    for char in name.upper():
        if char.isalpha():
            value = hebrew_values.get(char, 0)
            total += value
            calculation += f"{char} = {value}, "

    calculation = calculation.rstrip(', ')
    calculation += f" | Total: {total}"

    return calculation

def calculate_destiny_number(name):
    """Calculate destiny number from name"""
    # Simple calculation based on name length and vowels
    vowels = 'AEIOU'
    vowel_count = sum(1 for char in name.upper() if char in vowels)
    consonant_count = sum(1 for char in name.upper() if char.isalpha() and char not in vowels)

    # Reduce to single digit
    total = vowel_count + consonant_count
    while total > 9:
        total = sum(int(digit) for digit in str(total))

    return total

def generate_karmic_reading(birth_date, birth_time, birth_place, client_name):
    """Generate karmic analysis using AI"""
    try:
        # Create comprehensive prompt for AI karmic interpretation
        prompt = f"""
        En tant qu'expert en astrologie védique et analyse karmique, analysez le karma de {client_name}.

        DONNÉES ASTROLOGIQUES:
        Date de naissance: {birth_date.strftime('%d/%m/%Y')}
        Heure de naissance: {birth_time}
        Lieu de naissance: {birth_place}

        Fournissez une analyse karmique complète structurée en 4 sections:

        SECTION 1 - ANALYSE KARMIQUE GÉNÉRALE:
        - Patterns karmiques principaux révélés par la date de naissance
        - Influences planétaires sur le karma
        - Leçons principales de cette incarnation
        - Énergie karmique dominante

        SECTION 2 - VIES PASSÉES:
        - 2-3 incarnations significatives révélées par l'astrologie
        - Talents et compétences acquis dans le passé
        - Relations karmiques importantes
        - Événements marquants des vies antérieures

        SECTION 3 - BLOCAGES KARMIQUES:
        - Obstacles spirituels à surmonter
        - Patterns répétitifs à briser
        - Peurs et limitations karmiques
        - Clés pour la libération

        SECTION 4 - MISSION D'ÂME:
        - Purpose spirituel de cette incarnation
        - Talents à développer et partager
        - Service à rendre à l'humanité
        - Chemin vers l'évolution spirituelle

        Utilisez un ton mystique et bienveillant, avec des références à l'astrologie védique.
        Environ 600-700 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître en astrologie védique et analyse karmique. Vous possédez une connaissance approfondie des lois du karma, de la réincarnation et de l'évolution spirituelle. Votre style est mystique, bienveillant et profondément spirituel."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        karmic_analysis = ""
        past_lives = ""
        karmic_blocks = ""
        soul_mission = ""

        if "SECTION 1" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 5:
                karmic_analysis = "SECTION" + sections[1].strip()
                past_lives = "SECTION" + sections[2].strip()
                karmic_blocks = "SECTION" + sections[3].strip()
                soul_mission = "SECTION" + sections[4].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                quarter = len(lines) // 4
                karmic_analysis = '\n'.join(lines[:quarter])
                past_lives = '\n'.join(lines[quarter:2*quarter])
                karmic_blocks = '\n'.join(lines[2*quarter:3*quarter])
                soul_mission = '\n'.join(lines[3*quarter:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            quarter = len(lines) // 4
            karmic_analysis = '\n'.join(lines[:quarter])
            past_lives = '\n'.join(lines[quarter:2*quarter])
            karmic_blocks = '\n'.join(lines[2*quarter:3*quarter])
            soul_mission = '\n'.join(lines[3*quarter:])

        return karmic_analysis, past_lives, karmic_blocks, soul_mission

    except Exception as e:
        print(f"Error generating karmic reading: {e}")
        # Fallback
        karmic_analysis = f"Votre thème natal révèle des patterns karmiques importants pour {client_name}."
        past_lives = "Vos vies passées ont forgé votre âme et vos talents actuels."
        karmic_blocks = "Certains blocages demandent à être libérés pour votre évolution."
        soul_mission = "Votre mission d'âme vous appelle vers un service spirituel."
        return karmic_analysis, past_lives, karmic_blocks, soul_mission

def generate_pendulum_reading(question, pendulum_response, client_name):
    """Generate pendulum reading interpretation using AI"""
    try:
        # Create comprehensive prompt for AI pendulum interpretation
        prompt = f"""
        En tant qu'expert en radiesthésie et divination par pendule, interprétez cette consultation pour {client_name}.

        CONSULTATION PENDULE:
        Question posée: {question}
        Réponse du pendule: {pendulum_response.upper()}

        Fournissez une interprétation complète structurée en 2 sections:

        SECTION 1 - LECTURE ÉNERGÉTIQUE:
        - Signification énergétique de la réponse {pendulum_response.upper()}
        - Analyse des vibrations captées par le pendule
        - État énergétique actuel de la situation
        - Forces en présence (positives/négatives)
        - Timing et synchronicités

        SECTION 2 - GUIDANCE COMPLÉMENTAIRE:
        - Conseils pratiques basés sur la réponse
        - Actions recommandées ou à éviter
        - Signes à observer dans les prochains jours
        - Méditations ou pratiques spirituelles
        - Message de guidance supérieure

        Utilisez un ton mystique et bienveillant, avec des références à l'énergie subtile.
        Environ 300-400 mots au total, structuré clairement.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un expert en radiesthésie et divination par pendule. Vous possédez une connaissance approfondie des énergies subtiles et de l'interprétation des réponses du pendule. Votre style est mystique, bienveillant et profondément intuitif."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.8
        )

        full_interpretation = response.choices[0].message.content

        # Split into sections
        energy_reading = ""
        guidance = ""

        if "SECTION 1" in full_interpretation and "SECTION 2" in full_interpretation:
            sections = full_interpretation.split("SECTION")
            if len(sections) >= 3:
                energy_reading = "SECTION" + sections[1].strip()
                guidance = "SECTION" + sections[2].strip()
            else:
                # Fallback split
                lines = full_interpretation.split('\n')
                half = len(lines) // 2
                energy_reading = '\n'.join(lines[:half])
                guidance = '\n'.join(lines[half:])
        else:
            # Basic fallback
            lines = full_interpretation.split('\n')
            half = len(lines) // 2
            energy_reading = '\n'.join(lines[:half])
            guidance = '\n'.join(lines[half:])

        return energy_reading, guidance

    except Exception as e:
        print(f"Error generating pendulum reading: {e}")
        # Fallback
        response_meanings = {
            'oui': "L'énergie est favorable et vous encourage à avancer.",
            'non': "L'énergie suggère la prudence et la réflexion.",
            'peut-etre': "L'énergie est en transition, le timing n'est pas encore optimal."
        }
        energy_reading = response_meanings.get(pendulum_response, "L'énergie révèle des messages importants.")
        guidance = f"Méditez sur cette réponse et restez attentif aux signes de l'univers, {client_name}."
        return energy_reading, guidance

def generate_horoscope(birth_date, birth_time, birth_place, current_date, prediction_months=3, prediction_years=0):
    """Generate AI-powered horoscope data with extended predictions"""
    try:
        from datetime import datetime, timedelta
        import calendar

        # Parse dates
        birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d')
        current_date_obj = datetime.strptime(current_date, '%Y-%m-%d') if current_date else datetime.now()

        # Calculate sun sign based on birth date
        sun_sign = calculate_sun_sign(birth_date_obj)

        # Generate specific month and year lists
        month_names = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                      'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']

        # Generate monthly predictions list
        monthly_predictions = []
        if prediction_months > 0:
            for i in range(prediction_months):
                future_date = current_date_obj + timedelta(days=30*i)
                month_name = month_names[future_date.month - 1]
                year = future_date.year
                monthly_predictions.append(f"{month_name} {year}")

        # Generate yearly predictions list
        yearly_predictions = []
        if prediction_years > 0:
            start_year = current_date_obj.year
            for i in range(prediction_years):
                yearly_predictions.append(str(start_year + i))

        # Create comprehensive prompt for AI horoscope
        prediction_text = ""
        if prediction_months > 0:
            months_list = ", ".join(monthly_predictions)
            prediction_text += f"\n\n4. PRÉDICTIONS MENSUELLES ({prediction_months} mois):\n"
            prediction_text += f"- Fournissez des prédictions spécifiques pour chaque mois : {months_list}\n"
            prediction_text += "- Pour chaque mois, commencez par le nom du mois et l'année (ex: 'Mars 2025 :')\n"
            prediction_text += "- Analysez les transits mensuels et leurs influences\n"
            prediction_text += "- Donnez des conseils pratiques pour chaque période\n"

        if prediction_years > 0:
            years_list = ", ".join(yearly_predictions)
            prediction_text += f"\n\n5. PRÉDICTIONS ANNUELLES ({prediction_years} an{'s' if prediction_years > 1 else ''}):\n"
            prediction_text += f"- Décrivez les grandes tendances pour chaque année : {years_list}\n"
            prediction_text += "- Pour chaque année, commencez par l'année (ex: '2025 :')\n"
            prediction_text += "- Analysez les cycles astrologiques majeurs\n"
            prediction_text += "- Identifiez les périodes clés de transformation\n"

        prompt = f"""
        En tant qu'astrologue expert français, créez un horoscope personnalisé complet.

        INFORMATIONS DE NAISSANCE:
        Date de naissance: {birth_date_obj.strftime('%d/%m/%Y')}
        Heure de naissance: {birth_time if birth_time else "Non spécifiée"}
        Lieu de naissance: {birth_place}
        Signe solaire calculé: {sun_sign}

        DATE ACTUELLE: {current_date_obj.strftime('%d/%m/%Y')}

        ANALYSE REQUISE:
        Fournissez une analyse astrologique complète qui inclut:

        1. THÈME NATAL PRINCIPAL:
        - Analyse du signe solaire et ses caractéristiques
        - Influence de la position planétaire à cette date
        - Traits de personnalité dominants

        2. INFLUENCES ACTUELLES:
        - Transits planétaires du moment
        - Énergies astrologiques présentes
        - Périodes favorables et défis

        3. PRÉDICTIONS ET CONSEILS GÉNÉRAUX:
        - Guidance pour les prochaines semaines
        - Domaines de vie à privilégier
        - Conseils spirituels personnalisés{prediction_text}

        IMPORTANT: Répondez uniquement en TEXTE SIMPLE, sans balises HTML.
        Structurez votre réponse en paragraphes clairs séparés par des sauts de ligne.
        Utilisez un ton mystique, bienveillant et profondément spirituel.
        Faites environ {600 + (prediction_months * 50) + (prediction_years * 100)}-{800 + (prediction_months * 80) + (prediction_years * 150)} mots au total.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        # Calculate max tokens based on prediction periods
        base_tokens = 700
        monthly_tokens = prediction_months * 100
        yearly_tokens = prediction_years * 150
        max_tokens = min(4000, base_tokens + monthly_tokens + yearly_tokens)

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître astrologue français expert en thèmes nataux et prédictions astrologiques. Vous possédez une connaissance approfondie des influences planétaires, des signes du zodiaque et des transits. Votre style est mystique, précis et bienveillant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=max_tokens,
            temperature=0.8
        )

        interpretation = response.choices[0].message.content

        # Generate additional astrological data
        moon_sign = calculate_moon_sign(birth_date_obj)
        rising_sign = calculate_rising_sign(birth_date_obj, birth_place)

        return {
            'sun_sign': sun_sign,
            'moon_sign': moon_sign,
            'rising_sign': rising_sign,
            'interpretation': interpretation,
            'current_transits': generate_current_transits(),
            'prediction_months': prediction_months,
            'prediction_years': prediction_years,
            'monthly_predictions': monthly_predictions,
            'yearly_predictions': yearly_predictions,
            'current_date': current_date_obj.strftime('%d/%m/%Y')
        }

    except Exception as e:
        print(f"Error generating AI horoscope: {e}")
        # Fallback horoscope
        sun_sign = calculate_sun_sign(datetime.strptime(birth_date, '%Y-%m-%d'))
        return {
            'sun_sign': sun_sign,
            'moon_sign': 'Cancer',
            'rising_sign': 'Lion',
            'interpretation': f"Votre thème natal révèle une personnalité riche et complexe. En tant que {sun_sign}, vous possédez des qualités uniques qui vous distinguent. Les énergies actuelles vous invitent à explorer votre potentiel spirituel et à faire confiance à votre intuition. Cette période est favorable pour la croissance personnelle et les nouveaux commencements."
        }

def calculate_sun_sign(birth_date):
    """Calculate sun sign based on birth date"""
    month = birth_date.month
    day = birth_date.day

    if (month == 3 and day >= 21) or (month == 4 and day <= 19):
        return "Bélier"
    elif (month == 4 and day >= 20) or (month == 5 and day <= 20):
        return "Taureau"
    elif (month == 5 and day >= 21) or (month == 6 and day <= 20):
        return "Gémeaux"
    elif (month == 6 and day >= 21) or (month == 7 and day <= 22):
        return "Cancer"
    elif (month == 7 and day >= 23) or (month == 8 and day <= 22):
        return "Lion"
    elif (month == 8 and day >= 23) or (month == 9 and day <= 22):
        return "Vierge"
    elif (month == 9 and day >= 23) or (month == 10 and day <= 22):
        return "Balance"
    elif (month == 10 and day >= 23) or (month == 11 and day <= 21):
        return "Scorpion"
    elif (month == 11 and day >= 22) or (month == 12 and day <= 21):
        return "Sagittaire"
    elif (month == 12 and day >= 22) or (month == 1 and day <= 19):
        return "Capricorne"
    elif (month == 1 and day >= 20) or (month == 2 and day <= 18):
        return "Verseau"
    else:  # (month == 2 and day >= 19) or (month == 3 and day <= 20)
        return "Poissons"

def calculate_moon_sign(birth_date):
    """Calculate approximate moon sign (simplified calculation)"""
    signs = ["Bélier", "Taureau", "Gémeaux", "Cancer", "Lion", "Vierge",
             "Balance", "Scorpion", "Sagittaire", "Capricorne", "Verseau", "Poissons"]
    # Simplified calculation based on day of year
    day_of_year = birth_date.timetuple().tm_yday
    sign_index = (day_of_year * 12 // 365) % 12
    return signs[sign_index]

def calculate_rising_sign(birth_date, birth_place):
    """Calculate approximate rising sign (simplified calculation)"""
    signs = ["Bélier", "Taureau", "Gémeaux", "Cancer", "Lion", "Vierge",
             "Balance", "Scorpion", "Sagittaire", "Capricorne", "Verseau", "Poissons"]
    # Simplified calculation based on birth place hash and date
    place_hash = hash(birth_place.lower()) % 12
    date_factor = birth_date.day % 12
    sign_index = (place_hash + date_factor) % 12
    return signs[sign_index]

def generate_current_transits():
    """Generate current astrological transits"""
    from datetime import datetime
    current_date = datetime.now()

    # Sample current transits (in real app, this would use ephemeris data)
    transits = [
        {
            'planet': 'Jupiter',
            'sign': 'Taureau',
            'influence': 'Période favorable pour la stabilité financière et les projets à long terme'
        },
        {
            'planet': 'Saturne',
            'sign': 'Poissons',
            'influence': 'Temps de réflexion spirituelle et de restructuration intérieure'
        },
        {
            'planet': 'Mars',
            'sign': 'Gémeaux',
            'influence': 'Énergie communicative et intellectuelle, favorable aux échanges'
        },
        {
            'planet': 'Vénus',
            'sign': 'Cancer',
            'influence': 'Harmonisation des relations familiales et émotionnelles'
        }
    ]

    return transits

def generate_horoscope_pdf(horoscope_data, user):
    """Generate PDF for horoscope"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import html

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=1*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7B1FA2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#D4AF37')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20,
            rightIndent=20
        )

        # Build PDF content
        story = []

        # Title
        story.append(Paragraph("⭐ HOROSCOPE PERSONNALISÉ", title_style))
        story.append(Paragraph("Temple Du Voile - Analyse Astrologique Experte", styles['Normal']))
        story.append(Spacer(1, 30))

        # Date and user info
        story.append(Paragraph(f"<b>Consultation pour :</b> {user.username}", styles['Normal']))
        if user.birth_date:
            story.append(Paragraph(f"<b>Date de naissance :</b> {user.birth_date.strftime('%d/%m/%Y')}", styles['Normal']))
        if user.birth_time:
            story.append(Paragraph(f"<b>Heure de naissance :</b> {user.birth_time.strftime('%H:%M')}", styles['Normal']))
        if user.birth_place:
            story.append(Paragraph(f"<b>Lieu de naissance :</b> {user.birth_place}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Astrological signs
        story.append(Paragraph("🌟 VOTRE THÈME NATAL", subtitle_style))

        # Create table for signs
        signs_data = [
            ['Signe Solaire ☉', horoscope_data.get('sun_sign', 'Non calculé')],
            ['Signe Lunaire ☽', horoscope_data.get('moon_sign', 'Non calculé')],
            ['Ascendant ↗', horoscope_data.get('rising_sign', 'Non calculé')]
        ]

        signs_table = Table(signs_data, colWidths=[2.5*inch, 2.5*inch])
        signs_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#2D1B69')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#D4AF37'))
        ]))

        story.append(signs_table)
        story.append(Spacer(1, 20))

        # Interpretation
        story.append(Paragraph("🔮 ANALYSE ASTROLOGIQUE EXPERTE", subtitle_style))

        # Clean and format interpretation text
        interpretation_text = horoscope_data.get('interpretation', 'Analyse non disponible')
        interpretation_text = interpretation_text.replace('\n', '<br/>')
        interpretation_text = html.escape(interpretation_text).replace('&lt;br/&gt;', '<br/>')

        story.append(Paragraph(interpretation_text, body_style))
        story.append(Spacer(1, 20))

        # Current transits if available
        if horoscope_data.get('current_transits'):
            story.append(Paragraph("🔄 TRANSITS PLANÉTAIRES ACTUELS", subtitle_style))

            for transit in horoscope_data['current_transits']:
                transit_text = f"<b>{transit['planet']} en {transit['sign']} :</b> {transit['influence']}"
                story.append(Paragraph(transit_text, body_style))

            story.append(Spacer(1, 20))

        # Footer
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )

        story.append(Paragraph("✨ Temple Du Voile - Votre Guide Spirituel ✨", footer_style))
        story.append(Paragraph("Analyse astrologique experte et thème natal personnalisé", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except ImportError:
        # Fallback if reportlab is not installed
        return generate_simple_horoscope_pdf(horoscope_data, user)
    except Exception as e:
        print(f"Error generating horoscope PDF: {e}")
        return generate_simple_horoscope_pdf(horoscope_data, user)

def generate_simple_horoscope_pdf(horoscope_data, user):
    """Generate simple text-based PDF fallback for horoscope"""
    from io import BytesIO

    # Simple text content
    content = f"""
HOROSCOPE PERSONNALISÉ - TEMPLE DU VOILE
=======================================

Consultation pour : {user.username}
Date de naissance : {user.birth_date.strftime('%d/%m/%Y') if user.birth_date else 'Non spécifiée'}
Heure de naissance : {user.birth_time.strftime('%H:%M') if user.birth_time else 'Non spécifiée'}
Lieu de naissance : {user.birth_place if user.birth_place else 'Non spécifié'}

VOTRE THÈME NATAL :
Signe Solaire : {horoscope_data.get('sun_sign', 'Non calculé')}
Signe Lunaire : {horoscope_data.get('moon_sign', 'Non calculé')}
Ascendant : {horoscope_data.get('rising_sign', 'Non calculé')}

ANALYSE ASTROLOGIQUE EXPERTE :
{horoscope_data.get('interpretation', 'Analyse non disponible')}
"""

    if horoscope_data.get('current_transits'):
        content += "\n\nTRANSITS PLANÉTAIRES ACTUELS :\n"
        for transit in horoscope_data['current_transits']:
            content += f"{transit['planet']} en {transit['sign']} : {transit['influence']}\n"

    content += """

---
Temple Du Voile - Votre Guide Spirituel
Analyse astrologique experte et thème natal personnalisé
"""

    # Return as bytes for download
    return content.encode('utf-8')

def generate_numerology_reading(full_name, birth_date):
    """Generate numerology reading based on name and birth date"""
    from datetime import datetime

    # Calculate Life Path Number
    birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d')
    life_path = calculate_life_path(birth_date_obj)

    # Calculate Expression Number
    expression_number = calculate_expression_number(full_name)

    # Calculate Soul Urge Number
    soul_urge = calculate_soul_urge_number(full_name)

    # Calculate Personality Number
    personality_number = calculate_personality_number(full_name)

    return {
        'life_path': life_path,
        'expression_number': expression_number,
        'soul_urge': soul_urge,
        'personality_number': personality_number,
        'interpretation': generate_numerology_interpretation(life_path, expression_number, soul_urge, personality_number, full_name, birth_date)
    }

def calculate_life_path(birth_date):
    """Calculate life path number from birth date"""
    total = birth_date.day + birth_date.month + birth_date.year
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_expression_number(full_name):
    """Calculate expression number from full name"""
    letter_values = {
        'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8, 'I': 9,
        'J': 1, 'K': 2, 'L': 3, 'M': 4, 'N': 5, 'O': 6, 'P': 7, 'Q': 8, 'R': 9,
        'S': 1, 'T': 2, 'U': 3, 'V': 4, 'W': 5, 'X': 6, 'Y': 7, 'Z': 8
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name if char.isalpha())
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_soul_urge_number(full_name):
    """Calculate soul urge number from vowels in name"""
    vowels = 'AEIOU'
    letter_values = {
        'A': 1, 'E': 5, 'I': 9, 'O': 6, 'U': 3
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name if char.upper() in vowels)
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def calculate_personality_number(full_name):
    """Calculate personality number from consonants in name"""
    vowels = 'AEIOU'
    letter_values = {
        'B': 2, 'C': 3, 'D': 4, 'F': 6, 'G': 7, 'H': 8, 'J': 1, 'K': 2, 'L': 3,
        'M': 4, 'N': 5, 'P': 7, 'Q': 8, 'R': 9, 'S': 1, 'T': 2, 'V': 4, 'W': 5,
        'X': 6, 'Y': 7, 'Z': 8
    }

    total = sum(letter_values.get(char.upper(), 0) for char in full_name
                if char.isalpha() and char.upper() not in vowels)
    while total > 9 and total not in [11, 22, 33]:
        total = sum(int(digit) for digit in str(total))
    return total

def generate_numerology_interpretation(life_path, expression_number, soul_urge, personality_number, full_name, birth_date):
    """Generate AI-powered numerology interpretation"""
    try:
        # Create comprehensive prompt for AI numerology interpretation
        prompt = f"""
        En tant qu'expert numérologue français et maître en sciences sacrées, créez une analyse numérologique complète et personnalisée.

        INFORMATIONS PERSONNELLES:
        Nom complet: {full_name}
        Date de naissance: {birth_date}

        NOMBRES CALCULÉS:
        - Chemin de Vie: {life_path}
        - Nombre d'Expression: {expression_number}
        - Élan du Cœur (Soul Urge): {soul_urge}
        - Nombre de Personnalité: {personality_number}

        ANALYSE REQUISE:
        Fournissez une interprétation numérologique complète qui inclut:

        1. ANALYSE DU CHEMIN DE VIE ({life_path}):
        - Mission de vie et leçons à apprendre
        - Défis et opportunités de croissance
        - Talents naturels et potentiels

        2. NOMBRE D'EXPRESSION ({expression_number}):
        - Talents et capacités innés
        - Façon de s'exprimer dans le monde
        - Carrière et réalisation professionnelle

        3. ÉLAN DU CŒUR ({soul_urge}):
        - Désirs profonds de l'âme
        - Motivations intérieures
        - Ce qui nourrit vraiment l'esprit

        4. NOMBRE DE PERSONNALITÉ ({personality_number}):
        - Image projetée vers l'extérieur
        - Première impression donnée
        - Masque social et apparence

        5. SYNTHÈSE ET CONSEILS:
        - Harmonie entre les différents nombres
        - Conseils pour l'épanouissement personnel
        - Guidance spirituelle personnalisée

        IMPORTANT: Répondez uniquement en TEXTE SIMPLE, sans balises HTML.
        Structurez votre réponse en paragraphes clairs séparés par des sauts de ligne.
        Utilisez un ton mystique, bienveillant et profondément spirituel.
        Faites environ 500-600 mots au total.
        Mentionnez spécifiquement chaque nombre dans votre analyse.
        """

        # Call OpenAI API
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un maître numérologue français expert en sciences sacrées et symbolisme des nombres. Vous possédez une connaissance approfondie de la numérologie pythagoricienne, kabbalistique et moderne. Votre style est mystique, précis et bienveillant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.8
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error generating AI numerology interpretation: {e}")
        # Fallback to basic interpretation
        return generate_basic_numerology_interpretation(life_path, expression_number, soul_urge, personality_number)

def generate_basic_numerology_interpretation(life_path, expression_number, soul_urge, personality_number):
    """Generate basic numerology interpretation as fallback"""
    interpretations = {
        1: "Leader naturel, indépendant et pionnier",
        2: "Coopératif, diplomatique et sensible",
        3: "Créatif, expressif et optimiste",
        4: "Pratique, organisé et travailleur",
        5: "Aventurier, libre et polyvalent",
        6: "Protecteur, responsable et aimant",
        7: "Spirituel, analytique et introspectif",
        8: "Ambitieux, matérialiste et puissant",
        9: "Humanitaire, généreux et idéaliste",
        11: "Intuitif, inspiré et visionnaire",
        22: "Maître bâtisseur, pratique et visionnaire",
        33: "Maître enseignant, altruiste et guérisseur"
    }

    life_path_meaning = interpretations.get(life_path, "Nombre unique avec des qualités spéciales")
    expression_meaning = interpretations.get(expression_number, "Talents particuliers à développer")
    soul_urge_meaning = interpretations.get(soul_urge, "Désirs profonds uniques")
    personality_meaning = interpretations.get(personality_number, "Image extérieure distinctive")

    return f"""ANALYSE NUMÉROLOGIQUE EXPERTE

CHEMIN DE VIE {life_path}: Votre mission principale dans cette vie révèle que vous êtes {life_path_meaning}. Ce nombre guide votre évolution spirituelle et les leçons que vous devez apprendre.

NOMBRE D'EXPRESSION {expression_number}: Vos talents naturels montrent que vous avez {expression_meaning}. C'est la façon dont vous vous manifestez dans le monde et exprimez votre potentiel.

ÉLAN DU CŒUR {soul_urge}: Vos désirs profonds indiquent {soul_urge_meaning}. C'est ce qui motive vraiment votre âme et nourrit votre esprit.

NOMBRE DE PERSONNALITÉ {personality_number}: L'image que vous projetez révèle {personality_meaning}. C'est la première impression que vous donnez et votre masque social.

SYNTHÈSE: L'harmonie entre vos nombres révèle un être unique avec un potentiel extraordinaire. Votre chemin de vie vous guide vers votre mission, tandis que vos autres nombres vous donnent les outils pour l'accomplir. Embrassez cette sagesse numérologique pour révéler votre véritable nature."""

def generate_numerology_pdf(numerology_data):
    """Generate PDF for numerology reading"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
        from io import BytesIO
        import html

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=1*inch)

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#7B1FA2')
        )

        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#D4AF37')
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_JUSTIFY,
            leftIndent=20,
            rightIndent=20
        )

        number_style = ParagraphStyle(
            'NumberStyle',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=10,
            textColor=colors.HexColor('#7B1FA2'),
            fontName='Helvetica-Bold'
        )

        # Build PDF content
        story = []

        # Title
        story.append(Paragraph("🔢 PROFIL NUMÉROLOGIQUE", title_style))
        story.append(Paragraph("Temple Du Voile - Analyse Experte", styles['Normal']))
        story.append(Spacer(1, 30))

        # Date
        from datetime import datetime
        story.append(Paragraph(f"<b>Date d'analyse :</b> {datetime.now().strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Numbers section
        story.append(Paragraph("🌟 VOS NOMBRES PERSONNELS", subtitle_style))

        # Life Path Number
        story.append(Paragraph(f"<b>Chemin de Vie :</b> {numerology_data['life_path']}", number_style))
        story.append(Paragraph("Votre mission principale dans cette vie et les leçons à apprendre.", body_style))
        story.append(Spacer(1, 10))

        # Expression Number
        story.append(Paragraph(f"<b>Nombre d'Expression :</b> {numerology_data['expression_number']}", number_style))
        story.append(Paragraph("Vos talents naturels et la façon dont vous vous exprimez dans le monde.", body_style))
        story.append(Spacer(1, 10))

        # Soul Urge Number
        story.append(Paragraph(f"<b>Élan du Cœur :</b> {numerology_data['soul_urge']}", number_style))
        story.append(Paragraph("Vos désirs profonds et ce qui motive vraiment votre âme.", body_style))
        story.append(Spacer(1, 10))

        # Personality Number
        story.append(Paragraph(f"<b>Nombre de Personnalité :</b> {numerology_data['personality_number']}", number_style))
        story.append(Paragraph("L'image que vous projetez et la première impression que vous donnez.", body_style))
        story.append(Spacer(1, 20))

        # Interpretation
        story.append(Paragraph("🔮 INTERPRÉTATION PERSONNALISÉE", subtitle_style))

        # Clean and format interpretation text
        interpretation_text = numerology_data['interpretation'].replace('\n', '<br/>')
        interpretation_text = html.escape(interpretation_text).replace('&lt;br/&gt;', '<br/>')

        story.append(Paragraph(interpretation_text, body_style))
        story.append(Spacer(1, 30))

        # Footer
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )

        story.append(Paragraph("✨ Temple Du Voile - Votre Guide Spirituel ✨", footer_style))
        story.append(Paragraph("Analyse experte en numérologie et sciences sacrées", footer_style))

        # Build PDF
        doc.build(story)

        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()

        return pdf_content

    except ImportError:
        # Fallback if reportlab is not installed
        return generate_simple_numerology_pdf(numerology_data)
    except Exception as e:
        print(f"Error generating numerology PDF: {e}")
        return generate_simple_numerology_pdf(numerology_data)

def generate_simple_numerology_pdf(numerology_data):
    """Generate simple text-based PDF fallback for numerology"""
    from io import BytesIO
    from datetime import datetime

    # Simple text content
    content = f"""
PROFIL NUMÉROLOGIQUE - TEMPLE DU VOILE
=====================================

Date d'analyse : {datetime.now().strftime('%d/%m/%Y à %H:%M')}

VOS NOMBRES PERSONNELS :

Chemin de Vie : {numerology_data['life_path']}
Votre mission principale dans cette vie et les leçons à apprendre.

Nombre d'Expression : {numerology_data['expression_number']}
Vos talents naturels et la façon dont vous vous exprimez dans le monde.

Élan du Cœur : {numerology_data['soul_urge']}
Vos désirs profonds et ce qui motive vraiment votre âme.

Nombre de Personnalité : {numerology_data['personality_number']}
L'image que vous projetez et la première impression que vous donnez.

INTERPRÉTATION PERSONNALISÉE :
{numerology_data['interpretation']}

---
Temple Du Voile - Votre Guide Spirituel
Analyse experte en numérologie et sciences sacrées
"""

    # Return as bytes for download
    return content.encode('utf-8')

def create_sample_data():
    """Create sample courses and data for demonstration"""

    # Create complete Introduction aux Sciences Occultes course
    intro_course_data = {
        'title': 'Introduction aux Sciences Occultes',
        'description': 'Découvrez les fondements mystiques et spirituels des arts divinatoires. Ce cours complet explore l\'histoire, les principes fondamentaux et les pratiques des sciences occultes, de l\'Égypte ancienne aux techniques modernes de développement spirituel.',
        'category': 'Sciences Occultes',
        'level': 'standard',
        'instructor': 'Maître Éliphas Lévi'
    }

    existing_intro_course = Course.query.filter_by(title=intro_course_data['title']).first()
    if not existing_intro_course:
        intro_course = Course(**intro_course_data)
        db.session.add(intro_course)
        db.session.flush()  # Get the ID

        # Module 1: Origines Historiques
        module1 = Module(
            course_id=intro_course.id,
            title='Origines Historiques des Sciences Occultes',
            description='Explorez les racines anciennes des sciences occultes à travers les civilisations qui ont façonné notre compréhension moderne.',
            order=1
        )
        db.session.add(module1)
        db.session.flush()

        # Leçons Module 1
        lessons_module1 = [
            {
                'title': 'Les Civilisations Anciennes et leurs Mystères',
                'content': '''<h2>Les Civilisations Anciennes et leurs Mystères</h2>

<p>Les sciences occultes trouvent leurs racines dans les plus anciennes civilisations humaines. Dès l'aube de l'humanité, nos ancêtres ont cherché à comprendre les mystères de l'existence, à prédire l'avenir et à communiquer avec les forces invisibles qui gouvernent l'univers.</p>

<h3>L'Égypte Ancienne : Berceau de la Sagesse Ésotérique</h3>

<p>L'Égypte ancienne (3000 av. J.-C.) représente l'un des berceaux les plus importants des sciences occultes. Les prêtres égyptiens maîtrisaient :</p>

<ul>
<li><strong>L'Astrologie Sacrée :</strong> Ils ont créé le premier calendrier basé sur les cycles stellaires et lunaires</li>
<li><strong>L'Alchimie Primitive :</strong> Les techniques de momification révèlent une connaissance approfondie de la transformation de la matière</li>
<li><strong>La Magie Thaumaturgique :</strong> Les rituels de protection et de guérison décrits dans le Livre des Morts</li>
<li><strong>La Géométrie Sacrée :</strong> Les pyramides incarnent des proportions mathématiques parfaites liées aux cycles cosmiques</li>
</ul>

<p>Le <em>Livre des Morts</em> égyptien constitue l'un des premiers manuels spirituels, décrivant le voyage de l'âme après la mort et les techniques pour naviguer dans les plans subtils.</p>

<h3>La Mésopotamie : Naissance de l'Astrologie</h3>

<p>Les Babyloniens et les Assyriens (2500 av. J.-C.) ont développé :</p>

<ul>
<li><strong>Le Zodiaque :</strong> Division du ciel en 12 secteurs correspondant aux constellations</li>
<li><strong>L'Astrologie Judiciaire :</strong> Prédiction des événements politiques et personnels</li>
<li><strong>La Divination par les Entrailles :</strong> Lecture des présages dans les organes d'animaux sacrifiés</li>
<li><strong>La Magie Sympathique :</strong> Principe selon lequel "le semblable agit sur le semblable"</li>
</ul>

<p>Leurs observations astronomiques, gravées sur des tablettes cunéiformes, influencent encore notre astrologie moderne.</p>

<h3>Exercice Pratique</h3>

<p>Méditez sur cette question : "Quels mystères de l'existence vous interpellent le plus ?" Notez vos réflexions dans un carnet spirituel que vous tiendrez tout au long de ce cours.</p>''',
                'order': 1
            },
            {
                'title': 'L\'Héritage Grec : Philosophie et Mystères',
                'content': '''<h2>L'Héritage Grec : Philosophie et Mystères</h2>

<p>Les philosophes grecs ont rationalisé et systématisé les connaissances occultes, créant les bases théoriques qui perdurent aujourd'hui. Ils ont établi les correspondances mathématiques et métaphysiques qui sous-tendent les arts divinatoires.</p>

<h3>Pythagore (570-495 av. J.-C.) : Le Père de la Numérologie</h3>

<p>Pythagore a révolutionné notre compréhension des nombres en établissant :</p>

<ul>
<li><strong>Les Correspondances Numériques :</strong> Chaque nombre possède une vibration et une signification spirituelle</li>
<li><strong>L'Harmonie des Sphères :</strong> Les planètes émettent des sons musicaux selon leurs mouvements</li>
<li><strong>La Géométrie Sacrée :</strong> Les formes géométriques reflètent les lois cosmiques</li>
<li><strong>La Métempsycose :</strong> Doctrine de la réincarnation et de l'évolution de l'âme</li>
</ul>

<p>Sa célèbre maxime "Tout est nombre" reste le fondement de la numérologie moderne.</p>

<h3>Platon (428-348 av. J.-C.) : Les Mondes Invisibles</h3>

<p>Platon a développé la théorie des correspondances entre :</p>

<ul>
<li><strong>Le Monde des Idées :</strong> Réalité parfaite et éternelle</li>
<li><strong>Le Monde Sensible :</strong> Reflet imparfait du monde des Idées</li>
<li><strong>L'Âme Humaine :</strong> Pont entre les deux mondes</li>
</ul>

<p>Cette vision justifie théoriquement la divination : si tout est lié, les événements futurs peuvent être perçus dans les signes présents.</p>

<h3>Les Mystères d'Éleusis</h3>

<p>Ces initiations secrètes enseignaient :</p>

<ul>
<li>Les cycles de mort et de renaissance</li>
<li>La nature divine de l'âme humaine</li>
<li>Les techniques d'extase mystique</li>
<li>La communion avec les divinités</li>
</ul>

<h3>Plotin (205-270 ap. J.-C.) : Le Néoplatonisme</h3>

<p>Plotin a synthétisé la philosophie grecque en développant :</p>

<ul>
<li><strong>La Théorie de l'Émanation :</strong> Tout émane de l'Un divin</li>
<li><strong>Les Correspondances Universelles :</strong> Connexions entre tous les niveaux de réalité</li>
<li><strong>La Voie du Retour :</strong> Techniques pour remonter vers la Source divine</li>
</ul>

<p>Ces concepts forment la base métaphysique de toute pratique magique et divinatoire.</p>

<h3>Exercice de Réflexion</h3>

<p>Calculez votre nombre de destinée selon Pythagore : additionnez tous les chiffres de votre date de naissance jusqu'à obtenir un chiffre entre 1 et 9. Méditez sur sa signification.</p>''',
                'order': 2
            },
            {
                'title': 'Renaissance et Occultisme Moderne',
                'content': '''<h2>Renaissance et Occultisme Moderne</h2>

<p>La Renaissance (XVe-XVIe siècles) marque une résurgence extraordinaire des sciences occultes en Europe. Les érudits redécouvrent les textes antiques et développent de nouvelles synthèses ésotériques.</p>

<h3>Marsile Ficin (1433-1499) : La Magie Naturelle</h3>

<p>Ficin traduit le <em>Corpus Hermeticum</em> et développe :</p>

<ul>
<li><strong>La Magie Astrale :</strong> Utilisation des influences planétaires</li>
<li><strong>Les Talismans :</strong> Objets chargés d'énergies cosmiques</li>
<li><strong>La Musique Thérapeutique :</strong> Harmonisation par les sons</li>
<li><strong>La Médecine Astrologique :</strong> Soins selon les configurations célestes</li>
</ul>

<h3>Pic de la Mirandole (1463-1494) : La Synthèse Universelle</h3>

<p>Il tente de réconcilier :</p>

<ul>
<li>La Kabbale hébraïque</li>
<li>L'hermétisme égyptien</li>
<li>La philosophie grecque</li>
<li>La théologie chrétienne</li>
</ul>

<p>Sa vision d'une "philosophia perennis" influence encore l'ésotérisme moderne.</p>

<h3>Paracelse (1493-1541) : L'Alchimie Médicale</h3>

<p>Paracelse révolutionne la médecine en appliquant les principes alchimiques :</p>

<ul>
<li><strong>La Doctrine des Signatures :</strong> Les plantes ressemblent aux organes qu'elles soignent</li>
<li><strong>Les Trois Principes :</strong> Soufre (âme), Mercure (esprit), Sel (corps)</li>
<li><strong>La Médecine Spagyrique :</strong> Préparations alchimiques des remèdes</li>
<li><strong>L'Homme Microcosme :</strong> Correspondances entre l'humain et l'univers</li>
</ul>

<h3>John Dee (1527-1608) : La Magie Angélique</h3>

<p>Mathématicien et conseiller de la reine Élisabeth Ire, Dee développe :</p>

<ul>
<li><strong>La Magie Énochienne :</strong> Communication avec les anges</li>
<li><strong>La Géométrie Sacrée :</strong> Applications mathématiques de l'ésotérisme</li>
<li><strong>La Cryptographie Mystique :</strong> Codes secrets basés sur la Kabbale</li>
</ul>

<h3>L'Occultisme des XIXe-XXe Siècles</h3>

<h4>Éliphas Lévi (1810-1875)</h4>
<p>Renouveau de la magie occidentale avec :</p>
<ul>
<li>Synthèse de la Kabbale et du Tarot</li>
<li>Théorie de la Lumière Astrale</li>
<li>Rituels de haute magie</li>
</ul>

<h4>Helena Blavatsky (1831-1891)</h4>
<p>Fondatrice de la Théosophie, elle introduit :</p>
<ul>
<li>Les enseignements orientaux en Occident</li>
<li>La doctrine des Maîtres Ascensionnés</li>
<li>L'évolution spirituelle de l'humanité</li>
</ul>

<h4>Aleister Crowley (1875-1947)</h4>
<p>Développe la Thelema avec :</p>
<ul>
<li>La Loi de Thelema : "Fais ce que voudras"</li>
<li>Le système magique de l'A∴A∴</li>
<li>L'intégration de la psychologie moderne</li>
</ul>

<h3>L'Ésotérisme Contemporain</h3>

<p>Aujourd'hui, les sciences occultes intègrent :</p>

<ul>
<li><strong>La Psychologie Jungienne :</strong> Archétypes et inconscient collectif</li>
<li><strong>La Physique Quantique :</strong> Interconnexion universelle</li>
<li><strong>Les Neurosciences :</strong> Compréhension des états modifiés de conscience</li>
<li><strong>L'Intelligence Artificielle :</strong> Nouveaux outils d'analyse symbolique</li>
</ul>

<h3>Exercice Pratique</h3>

<p>Choisissez un personnage historique qui vous inspire parmi ceux étudiés. Recherchez une de ses œuvres principales et lisez-en un extrait. Notez comment ses idées résonnent avec votre quête spirituelle personnelle.</p>''',
                'order': 3
            }
        ]

        for lesson_data in lessons_module1:
            lesson = Lesson(
                module_id=module1.id,
                title=lesson_data['title'],
                content=lesson_data['content'],
                order=lesson_data['order']
            )
            db.session.add(lesson)

        # Module 2: Principes Fondamentaux
        module2 = Module(
            course_id=intro_course.id,
            title='Principes Fondamentaux des Sciences Occultes',
            description='Comprenez les lois universelles qui régissent les sciences occultes : correspondances, vibrations et symbolisme.',
            order=2
        )
        db.session.add(module2)
        db.session.flush()

        # Leçons Module 2
        lessons_module2 = [
            {
                'title': 'La Loi des Correspondances Universelles',
                'content': '''<h2>La Loi des Correspondances Universelles</h2>

<p>Le principe fondamental des sciences occultes repose sur la doctrine des correspondances, énoncée dans la célèbre Table d'Émeraude d'Hermès Trismégiste : <em>"Ce qui est en haut est comme ce qui est en bas, ce qui est en bas est comme ce qui est en haut."</em></p>

<h3>Les Trois Niveaux de Réalité</h3>

<h4>1. Le Macrocosme (L'Univers)</h4>
<ul>
<li><strong>Les Planètes :</strong> Influences cosmiques sur la destinée</li>
<li><strong>Les Constellations :</strong> Archétypes célestes</li>
<li><strong>Les Cycles Cosmiques :</strong> Rythmes universels</li>
<li><strong>Les Forces Élémentaires :</strong> Feu, Terre, Air, Eau</li>
</ul>

<h4>2. Le Mésocosme (La Nature)</h4>
<ul>
<li><strong>Les Saisons :</strong> Cycles de mort et renaissance</li>
<li><strong>Les Éléments Naturels :</strong> Manifestations terrestres des forces cosmiques</li>
<li><strong>Les Règnes :</strong> Minéral, végétal, animal</li>
<li><strong>Les Lieux Sacrés :</strong> Points de convergence énergétique</li>
</ul>

<h4>3. Le Microcosme (L'Être Humain)</h4>
<ul>
<li><strong>Le Corps Physique :</strong> Temple de l'âme</li>
<li><strong>L'Âme :</strong> Siège des émotions et désirs</li>
<li><strong>L'Esprit :</strong> Étincelle divine en l'homme</li>
<li><strong>La Conscience :</strong> Miroir de l'univers</li>
</ul>

<h3>Applications Pratiques des Correspondances</h3>

<h4>En Astrologie</h4>
<p>Les planètes correspondent aux différents aspects de la personnalité :</p>
<ul>
<li><strong>Soleil :</strong> Ego, volonté, vitalité</li>
<li><strong>Lune :</strong> Émotions, intuition, subconscient</li>
<li><strong>Mercure :</strong> Communication, intellect, adaptabilité</li>
<li><strong>Vénus :</strong> Amour, beauté, harmonie</li>
<li><strong>Mars :</strong> Action, courage, agressivité</li>
</ul>

<h4>En Numérologie</h4>
<p>Les nombres reflètent les principes cosmiques :</p>
<ul>
<li><strong>1 :</strong> Unité, commencement, leadership</li>
<li><strong>2 :</strong> Dualité, coopération, équilibre</li>
<li><strong>3 :</strong> Trinité, créativité, expression</li>
<li><strong>4 :</strong> Stabilité, ordre, matérialisation</li>
<li><strong>5 :</strong> Changement, liberté, aventure</li>
</ul>

<h4>En Tarot</h4>
<p>Les cartes correspondent aux archétypes universels :</p>
<ul>
<li><strong>Le Bateleur :</strong> Volonté créatrice, nouveau départ</li>
<li><strong>La Papesse :</strong> Sagesse intuitive, mystères</li>
<li><strong>L'Impératrice :</strong> Fécondité, abondance, nature</li>
<li><strong>L'Empereur :</strong> Autorité, structure, ordre</li>
</ul>

<h3>La Table des Correspondances Hermétiques</h3>

<table border="1" style="width:100%; border-collapse: collapse;">
<tr>
<th>Planète</th>
<th>Jour</th>
<th>Métal</th>
<th>Couleur</th>
<th>Pierre</th>
<th>Parfum</th>
</tr>
<tr>
<td>Soleil</td>
<td>Dimanche</td>
<td>Or</td>
<td>Jaune/Doré</td>
<td>Topaze</td>
<td>Oliban</td>
</tr>
<tr>
<td>Lune</td>
<td>Lundi</td>
<td>Argent</td>
<td>Blanc/Argenté</td>
<td>Perle</td>
<td>Jasmin</td>
</tr>
<tr>
<td>Mars</td>
<td>Mardi</td>
<td>Fer</td>
<td>Rouge</td>
<td>Rubis</td>
<td>Poivre</td>
</tr>
<tr>
<td>Mercure</td>
<td>Mercredi</td>
<td>Mercure</td>
<td>Orange</td>
<td>Agate</td>
<td>Storax</td>
</tr>
<tr>
<td>Jupiter</td>
<td>Jeudi</td>
<td>Étain</td>
<td>Bleu</td>
<td>Saphir</td>
<td>Cèdre</td>
</tr>
<tr>
<td>Vénus</td>
<td>Vendredi</td>
<td>Cuivre</td>
<td>Vert</td>
<td>Émeraude</td>
<td>Rose</td>
</tr>
<tr>
<td>Saturne</td>
<td>Samedi</td>
<td>Plomb</td>
<td>Noir/Violet</td>
<td>Onyx</td>
<td>Myrrhe</td>
</tr>
</table>

<h3>Exercice Pratique</h3>

<p>Choisissez un jour de la semaine et travaillez avec ses correspondances :</p>
<ol>
<li>Portez la couleur associée</li>
<li>Méditez avec la pierre correspondante</li>
<li>Brûlez l'encens approprié</li>
<li>Observez comment ces éléments influencent votre état d'esprit</li>
</ol>

<p>Notez vos observations dans votre carnet spirituel.</p>''',
                'order': 1
            },
            {
                'title': 'Vibrations et Énergies Subtiles',
                'content': '''<h2>Vibrations et Énergies Subtiles</h2>

<p>Tout dans l'univers vibre à une fréquence particulière. Les sciences occultes enseignent que ces vibrations peuvent être perçues, modifiées et utilisées pour influencer la réalité. Cette compréhension est à la base de tous les arts divinatoires et des pratiques magiques.</p>

<h3>Les Différents Plans Vibratoires</h3>

<h4>1. Plan Physique (Vibrations Denses)</h4>
<ul>
<li><strong>Matière Solide :</strong> Cristaux, métaux, pierres</li>
<li><strong>Liquides :</strong> Eau, huiles essentielles, élixirs</li>
<li><strong>Gaz :</strong> Encens, parfums, respirations</li>
<li><strong>Énergies :</strong> Électricité, magnétisme, chaleur</li>
</ul>

<h4>2. Plan Éthérique (Énergie Vitale)</h4>
<ul>
<li><strong>Prana/Chi :</strong> Force vitale universelle</li>
<li><strong>Aura :</strong> Champ énergétique personnel</li>
<li><strong>Chakras :</strong> Centres énergétiques du corps</li>
<li><strong>Méridiens :</strong> Canaux de circulation énergétique</li>
</ul>

<h4>3. Plan Astral (Émotions et Désirs)</h4>
<ul>
<li><strong>Émotions :</strong> Joie, peur, colère, amour</li>
<li><strong>Désirs :</strong> Aspirations et attachements</li>
<li><strong>Formes-Pensées :</strong> Créations mentales temporaires</li>
<li><strong>Égrégores :</strong> Entités collectives créées par les groupes</li>
</ul>

<h4>4. Plan Mental (Pensées et Idées)</h4>
<ul>
<li><strong>Pensées Concrètes :</strong> Logique, analyse, calcul</li>
<li><strong>Pensées Abstraites :</strong> Intuition, inspiration, créativité</li>
<li><strong>Archétypes :</strong> Modèles universels de la psyché</li>
<li><strong>Idées Pures :</strong> Concepts éternels et parfaits</li>
</ul>

<h3>Comment Percevoir les Vibrations</h3>

<h4>Développement de la Sensibilité</h4>
<ol>
<li><strong>Méditation Quotidienne :</strong> Calme le mental et ouvre la perception</li>
<li><strong>Exercices de Respiration :</strong> Harmonise les énergies internes</li>
<li><strong>Contact avec la Nature :</strong> Ressent les vibrations naturelles</li>
<li><strong>Jeûne Périodique :</strong> Purifie et affine les sens subtils</li>
</ol>

<h4>Signes de Perception Énergétique</h4>
<ul>
<li><strong>Sensations Physiques :</strong> Picotements, chaleur, froid</li>
<li><strong>Impressions Visuelles :</strong> Couleurs, lumières, formes</li>
<li><strong>Perceptions Auditives :</strong> Sons, musiques, voix intérieures</li>
<li><strong>Intuitions :</strong> Connaissances spontanées, pressentiments</li>
</ul>

<h3>Techniques de Modification Vibratoire</h3>

<h4>1. Sons et Mantras</h4>
<p>Les vibrations sonores transforment l'état de conscience :</p>
<ul>
<li><strong>OM :</strong> Vibration primordiale de l'univers</li>
<li><strong>AH :</strong> Ouverture du cœur et guérison</li>
<li><strong>HU :</strong> Connexion avec le divin</li>
<li><strong>Chants Grégoriens :</strong> Élévation spirituelle</li>
</ul>

<h4>2. Couleurs et Lumières</h4>
<p>Chaque couleur porte une fréquence spécifique :</p>
<ul>
<li><strong>Rouge :</strong> Vitalité, force, passion</li>
<li><strong>Orange :</strong> Créativité, joie, sociabilité</li>
<li><strong>Jaune :</strong> Intelligence, clarté mentale</li>
<li><strong>Vert :</strong> Guérison, équilibre, nature</li>
<li><strong>Bleu :</strong> Paix, communication, spiritualité</li>
<li><strong>Indigo :</strong> Intuition, vision intérieure</li>
<li><strong>Violet :</strong> Transformation, magie, mystère</li>
</ul>

<h4>3. Cristaux et Pierres</h4>
<p>Les minéraux émettent des vibrations stables :</p>
<ul>
<li><strong>Quartz Clair :</strong> Amplification et purification</li>
<li><strong>Améthyste :</strong> Spiritualité et protection</li>
<li><strong>Citrine :</strong> Abondance et confiance</li>
<li><strong>Hématite :</strong> Ancrage et protection</li>
</ul>

<h3>La Loi de Résonance</h3>

<p>Principe fondamental : <em>"Ce qui vibre ensemble s'attire mutuellement"</em></p>

<h4>Applications Pratiques</h4>
<ul>
<li><strong>Attraction :</strong> Élever sa vibration attire des expériences similaires</li>
<li><strong>Guérison :</strong> Harmoniser les fréquences déséquilibrées</li>
<li><strong>Divination :</strong> Se synchroniser avec les énergies à percevoir</li>
<li><strong>Manifestation :</strong> Aligner pensées, émotions et actions</li>
</ul>

<h3>Exercices Pratiques</h3>

<h4>Exercice 1 : Perception de l'Aura</h4>
<ol>
<li>Placez vos mains à 20 cm l'une de l'autre</li>
<li>Respirez calmement et concentrez-vous</li>
<li>Rapprochez lentement vos mains</li>
<li>Sentez la résistance énergétique entre elles</li>
<li>Notez les sensations : chaleur, picotements, pression</li>
</ol>

<h4>Exercice 2 : Harmonisation par les Sons</h4>
<ol>
<li>Choisissez un mantra (OM, AH, HU)</li>
<li>Chantez-le pendant 10 minutes</li>
<li>Observez les changements dans votre état</li>
<li>Notez les sensations physiques et émotionnelles</li>
</ol>

<h4>Exercice 3 : Méditation avec les Couleurs</h4>
<ol>
<li>Visualisez une lumière dorée au-dessus de votre tête</li>
<li>Laissez-la descendre et remplir tout votre corps</li>
<li>Sentez sa vibration purifiante et énergisante</li>
<li>Maintenez cette visualisation 15 minutes</li>
</ol>''',
                'order': 2
            },
            {
                'title': 'Symbolisme et Archétypes Universels',
                'content': '''<h2>Symbolisme et Archétypes Universels</h2>

<p>Les symboles sont le langage universel de l'inconscient et des plans subtils. Comprendre leur signification profonde est essentiel pour maîtriser les arts divinatoires et interpréter les messages spirituels.</p>

<h3>Nature et Fonction des Symboles</h3>

<h4>Définition du Symbole</h4>
<p>Un symbole est un signe qui :</p>
<ul>
<li><strong>Représente :</strong> Une réalité invisible par une image visible</li>
<li><strong>Condense :</strong> Plusieurs niveaux de signification en une forme</li>
<li><strong>Évoque :</strong> Des résonances émotionnelles et spirituelles</li>
<li><strong>Transcende :</strong> Les limitations du langage ordinaire</li>
</ul>

<h4>Différence entre Signe et Symbole</h4>
<ul>
<li><strong>Signe :</strong> Relation conventionnelle (feu rouge = arrêt)</li>
<li><strong>Symbole :</strong> Relation naturelle et universelle (feu = purification, passion, destruction créatrice)</li>
</ul>

<h3>Les Archétypes de Carl Gustav Jung</h3>

<p>Jung a identifié des modèles universels présents dans l'inconscient collectif de l'humanité :</p>

<h4>Archétypes Principaux</h4>

<h5>1. Le Soi (Self)</h5>
<ul>
<li><strong>Symboles :</strong> Mandala, cercle, croix, étoile</li>
<li><strong>Signification :</strong> Totalité psychique, centre spirituel</li>
<li><strong>Dans le Tarot :</strong> Le Monde, Le Soleil</li>
</ul>

<h5>2. L'Ombre (Shadow)</h5>
<ul>
<li><strong>Symboles :</strong> Démon, dragon, sorcière, ténèbres</li>
<li><strong>Signification :</strong> Aspects refoulés de la personnalité</li>
<li><strong>Dans le Tarot :</strong> Le Diable, La Lune</li>
</ul>

<h5>3. L'Anima/Animus</h5>
<ul>
<li><strong>Anima (féminin en l'homme) :</strong> Intuition, réceptivité, émotion</li>
<li><strong>Animus (masculin en la femme) :</strong> Logique, action, volonté</li>
<li><strong>Dans le Tarot :</strong> La Papesse/L'Empereur</li>
</ul>

<h5>4. Le Sage (Wise Old Man)</h5>
<ul>
<li><strong>Symboles :</strong> Ermite, magicien, guide spirituel</li>
<li><strong>Signification :</strong> Sagesse, connaissance, guidance</li>
<li><strong>Dans le Tarot :</strong> L'Hermite, Le Pape</li>
</ul>

<h5>5. La Grande Mère (Great Mother)</h5>
<ul>
<li><strong>Symboles :</strong> Terre, lune, eau, grotte</li>
<li><strong>Signification :</strong> Nourricière, protectrice, créatrice</li>
<li><strong>Dans le Tarot :</strong> L'Impératrice, L'Étoile</li>
</ul>

<h3>Symboles Universels et leurs Significations</h3>

<h4>Formes Géométriques</h4>

<h5>Le Cercle</h5>
<ul>
<li><strong>Significations :</strong> Perfection, éternité, cycles, protection</li>
<li><strong>Applications :</strong> Mandalas, cercles magiques, roue zodiacale</li>
<li><strong>Psychologie :</strong> Totalité du Soi, complétude</li>
</ul>

<h5>Le Triangle</h5>
<ul>
<li><strong>Pointe vers le haut :</strong> Feu, masculin, élévation spirituelle</li>
<li><strong>Pointe vers le bas :</strong> Eau, féminin, descente dans la matière</li>
<li><strong>Applications :</strong> Pyramides, symboles alchimiques</li>
</ul>

<h5>Le Carré</h5>
<ul>
<li><strong>Significations :</strong> Stabilité, matière, terre, ordre</li>
<li><strong>Applications :</strong> Temples, autels, fondations</li>
<li><strong>Psychologie :</strong> Structure, sécurité, limites</li>
</ul>

<h5>La Croix</h5>
<ul>
<li><strong>Significations :</strong> Union des opposés, sacrifice, rédemption</li>
<li><strong>Variantes :</strong> Croix chrétienne, ankh égyptien, croix celtique</li>
<li><strong>Applications :</strong> Points cardinaux, intersection des plans</li>
</ul>

<h4>Éléments Naturels</h4>

<h5>Le Feu</h5>
<ul>
<li><strong>Positif :</strong> Purification, passion, illumination, énergie</li>
<li><strong>Négatif :</strong> Destruction, colère, violence, consumation</li>
<li><strong>Correspondances :</strong> Sud, été, midi, rouge</li>
</ul>

<h5>L'Eau</h5>
<ul>
<li><strong>Positif :</strong> Purification, guérison, intuition, émotion</li>
<li><strong>Négatif :</strong> Noyade, stagnation, illusion, chaos</li>
<li><strong>Correspondances :</strong> Ouest, automne, crépuscule, bleu</li>
</ul>

<h5>L'Air</h5>
<ul>
<li><strong>Positif :</strong> Communication, intellect, liberté, inspiration</li>
<li><strong>Négatif :</strong> Dispersion, superficialité, instabilité</li>
<li><strong>Correspondances :</strong> Est, printemps, aube, jaune</li>
</ul>

<h5>La Terre</h5>
<ul>
<li><strong>Positif :</strong> Stabilité, fertilité, abondance, ancrage</li>
<li><strong>Négatif :</strong> Lourdeur, matérialisme, stagnation</li>
<li><strong>Correspondances :</strong> Nord, hiver, minuit, vert</li>
</ul>

<h4>Animaux Symboliques</h4>

<h5>Le Lion</h5>
<ul>
<li><strong>Significations :</strong> Courage, royauté, force solaire, orgueil</li>
<li><strong>Dans le Tarot :</strong> La Force, Le Soleil</li>
<li><strong>Astrologie :</strong> Signe du Lion, Soleil</li>
</ul>

<h5>L'Aigle</h5>
<ul>
<li><strong>Significations :</strong> Vision spirituelle, élévation, messager divin</li>
<li><strong>Correspondances :</strong> Élément Air, hauteurs, liberté</li>
<li><strong>Alchimie :</strong> Sublimation, transformation</li>
</ul>

<h5>Le Serpent</h5>
<ul>
<li><strong>Positif :</strong> Sagesse, guérison, transformation, kundalini</li>
<li><strong>Négatif :</strong> Tentation, poison, tromperie</li>
<li><strong>Cycles :</strong> Mue = renaissance, ouroboros = éternité</li>
</ul>

<h3>Application Pratique du Symbolisme</h3>

<h4>En Divination</h4>
<ul>
<li><strong>Tarot :</strong> Interpréter les symboles des cartes</li>
<li><strong>Rêves :</strong> Décoder les messages de l'inconscient</li>
<li><strong>Signes :</strong> Reconnaître les synchronicités</li>
<li><strong>Méditation :</strong> Utiliser les symboles comme supports</li>
</ul>

<h4>En Développement Personnel</h4>
<ul>
<li><strong>Identification :</strong> Reconnaître ses archétypes dominants</li>
<li><strong>Intégration :</strong> Harmoniser les aspects contradictoires</li>
<li><strong>Transformation :</strong> Utiliser les symboles pour évoluer</li>
<li><strong>Créativité :</strong> S'inspirer des archétypes universels</li>
</ul>

<h3>Exercices Pratiques</h3>

<h4>Exercice 1 : Journal des Symboles</h4>
<ol>
<li>Tenez un carnet de vos rêves pendant une semaine</li>
<li>Notez tous les symboles qui apparaissent</li>
<li>Recherchez leurs significations traditionnelles</li>
<li>Réfléchissez à leur sens personnel pour vous</li>
</ol>

<h4>Exercice 2 : Méditation sur un Archétype</h4>
<ol>
<li>Choisissez un archétype qui vous attire (Sage, Guerrier, Mère...)</li>
<li>Visualisez-le pendant 15 minutes</li>
<li>Dialoguez mentalement avec lui</li>
<li>Demandez-lui un conseil pour votre vie actuelle</li>
</ol>

<h4>Exercice 3 : Création d'un Mandala Personnel</h4>
<ol>
<li>Dessinez un cercle sur une feuille</li>
<li>Remplissez-le avec des symboles qui vous représentent</li>
<li>Utilisez des couleurs intuitives</li>
<li>Méditez sur votre création terminée</li>
</ol>''',
                'order': 3
            }
        ]

        for lesson_data in lessons_module2:
            lesson = Lesson(
                module_id=module2.id,
                title=lesson_data['title'],
                content=lesson_data['content'],
                order=lesson_data['order']
            )
            db.session.add(lesson)

    # Sample courses
    courses_data = [
        {
            'title': 'Les Mystères de la Kabbale',
            'description': 'Explorez la sagesse ancestrale de la Kabbale, ses symboles, l\'Arbre de Vie et les chemins de l\'illumination spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'premium',
            'instructor': 'Rabbi Sarah'
        },
        {
            'title': 'Parapsychologie Moderne',
            'description': 'Étudiez les phénomènes paranormaux avec une approche scientifique moderne. Télépathie, psychokinèse et perception extrasensorielle.',
            'category': 'Parapsychologie',
            'level': 'premium',
            'instructor': 'Dr. Michel Dubois'
        },
        {
            'title': 'Traditions Ésotériques Africaines',
            'description': 'Plongez dans la richesse des traditions spirituelles africaines, leurs rituels, leurs divinités et leur sagesse millénaire.',
            'category': 'Ésotérisme Africain',
            'level': 'gold',
            'instructor': 'Mama Aisha'
        },
        {
            'title': 'Maîtrise du Tarot de Marseille',
            'description': 'Apprenez à lire et interpréter le Tarot de Marseille. De l\'histoire des cartes aux tirages complexes.',
            'category': 'Tarot et Divination',
            'level': 'standard',
            'instructor': 'Madame Solange'
        },
        {
            'title': 'Alchimie et Transformation',
            'description': 'Découvrez l\'art royal de l\'alchimie, ses symboles hermétiques et les voies de la transmutation spirituelle.',
            'category': 'Sciences Occultes',
            'level': 'gold',
            'instructor': 'Frère Nicolas'
        }
    ]

    for course_data in courses_data:
        existing_course = Course.query.filter_by(title=course_data['title']).first()
        if not existing_course:
            course = Course(**course_data)
            db.session.add(course)

            # Add sample modules for each course
            for i in range(1, 4):
                module = Module(
                    course=course,
                    title=f'Module {i}: {course_data["title"][:20]}...',
                    description=f'Description du module {i}',
                    order=i
                )
                db.session.add(module)

                # Add sample lessons for each module
                for j in range(1, 4):
                    lesson = Lesson(
                        module=module,
                        title=f'Leçon {j}: Introduction',
                        content=f'Contenu de la leçon {j} du module {i}',
                        order=j
                    )
                    db.session.add(lesson)

    # Sample products for marketplace
    products_data = [
        {
            'name': 'Jeu de Tarot de Marseille Authentique',
            'description': 'Jeu de tarot traditionnel de Marseille, reproduction fidèle des cartes historiques.',
            'price': 29.99,
            'category': 'Cartes et Oracles',
            'stock': 50
        },
        {
            'name': 'Pendule en Améthyste',
            'description': 'Pendule divinatoire en améthyste naturelle, parfait pour la radiesthésie.',
            'price': 45.00,
            'category': 'Outils Divinatoires',
            'stock': 25
        },
        {
            'name': 'Encens de Purification',
            'description': 'Mélange d\'encens pour purifier l\'espace et élever les vibrations.',
            'price': 15.99,
            'category': 'Encens et Résines',
            'stock': 100
        },
        {
            'name': 'Consultation Privée - 1h',
            'description': 'Consultation personnalisée avec un expert en sciences occultes.',
            'price': 80.00,
            'category': 'Consultations',
            'stock': 10
        }
    ]

    for product_data in products_data:
        existing_product = Product.query.filter_by(name=product_data['name']).first()
        if not existing_product:
            product = Product(**product_data)
            db.session.add(product)

    db.session.commit()
    print("Sample data created successfully!")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>', subscription_level='gold', is_admin=True)
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Admin user created: admin/admin123")

        # Create sample data
        create_sample_data()

    app.run(debug=True, host='0.0.0.0', port=5001)

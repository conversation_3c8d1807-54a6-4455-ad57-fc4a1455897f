# L'Arcanum Éveillé 🌙✨

Une plateforme ésotérique française complète dédiée aux sciences occultes, à la parapsychologie et à l'ésotérisme africain.

## 🎯 Description

L'Arcanum Éveillé est votre sanctuaire numérique pour explorer en profondeur les mystères des sciences occultes. La plateforme offre des cours exclusifs, des outils divinatoires interactifs, une communauté d'initiés et une marketplace spécialisée.

## ✨ Fonctionnalités Principales

### 🎓 Système de Cours
- **Bibliothèque complète** de cours sur les sciences occultes
- **Niveaux d'abonnement** : Standard, Premium, Gold
- **Lecteur de cours intégré** avec progression
- **Modules et leçons structurés**
- **Système de notes personnelles**

### 🔮 Outils Ésotériques Interactifs
- **Interprétation des rêves** par IA avancée
- **Lectures de tarot** (simple, 3 cartes, croix celtique)
- **Horoscope personnel** avec carte du ciel
- **Historique des consultations**

### 👥 Communauté
- **Forums de discussion** par thématique
- **Système de messagerie** entre membres
- **Événements et webinaires** exclusifs

### 🛒 Marketplace
- **Produits ésotériques** (cartes, pendules, encens)
- **Consultations privées** avec experts
- **Système de commandes** intégré

### 🎨 Interface Utilisateur
- **Thème sombre mystique** avec accents dorés
- **Design responsive** pour tous les appareils
- **Animations et effets visuels** immersifs
- **Navigation intuitive**

## 🚀 Installation

### Prérequis
- Python 3.8+
- pip (gestionnaire de paquets Python)

### Étapes d'installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd science_occultes
```

2. **Créer un environnement virtuel**
```bash
python3 -m venv venv
source venv/bin/activate  # Sur Windows: venv\Scripts\activate
```

3. **Installer les dépendances**
```bash
pip install -r requirements.txt
```

4. **Configuration des variables d'environnement**
```bash
cp .env.example .env
# Éditer le fichier .env avec vos clés API
```

5. **Lancer l'application**
```bash
python app.py
```

6. **Accéder à l'application**
Ouvrez votre navigateur et allez à : `http://127.0.0.1:5000`

## 🔑 Comptes de Démonstration

### Compte Administrateur
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`
- **Niveau** : Gold (accès complet)

## 📁 Structure du Projet

```
science_occultes/
├── app.py                 # Application Flask principale
├── config.py             # Configuration de l'application
├── models.py             # Modèles de base de données
├── requirements.txt      # Dépendances Python
├── templates/            # Templates HTML
│   ├── base.html        # Template de base
│   ├── index.html       # Page d'accueil
│   ├── login.html       # Page de connexion
│   ├── register.html    # Page d'inscription
│   ├── dashboard.html   # Tableau de bord
│   ├── courses.html     # Liste des cours
│   ├── course_detail.html # Détail d'un cours
│   ├── course_player.html # Lecteur de cours
│   └── tools/           # Outils ésotériques
│       ├── index.html   # Hub des outils
│       ├── dreams.html  # Interprétation des rêves
│       └── tarot.html   # Lectures de tarot
├── static/              # Fichiers statiques
│   ├── css/
│   │   └── style.css    # Styles CSS personnalisés
│   ├── js/
│   │   └── main.js      # JavaScript principal
│   └── images/          # Images et icônes
└── venv/                # Environnement virtuel Python
```

## 🎨 Thème et Design

### Palette de Couleurs
- **Arrière-plan** : Noir profond (#0a0a0a)
- **Secondaire** : Gris foncé (#1a1a1a, #2a2a2a)
- **Accent principal** : Or (#d4af37)
- **Accent mystique** : Violet profond (#4a148c)
- **Accent spirituel** : Bleu mystique (#1a237e)

### Typographie
- **Titres mystiques** : Cinzel (serif élégant)
- **Corps de texte** : Crimson Text (serif lisible)

## 🔧 Technologies Utilisées

### Backend
- **Flask** - Framework web Python
- **SQLAlchemy** - ORM pour base de données
- **SQLite** - Base de données légère
- **Flask-Login** - Gestion des sessions utilisateur
- **Werkzeug** - Utilitaires web

### Frontend
- **Bootstrap 5** - Framework CSS responsive
- **Font Awesome** - Icônes
- **JavaScript Vanilla** - Interactions dynamiques
- **CSS3** - Animations et effets visuels

### APIs Externes
- **OpenAI** - Interprétation des rêves par IA
- **Stripe** - Paiements sécurisés (configuration)

## 📊 Niveaux d'Abonnement

### 🥉 Standard (19.99€/mois)
- Accès aux cours de base
- Forum limité
- Interprétation des rêves (IA basique)
- Tirage de tarot simple (1 carte)

### 🥈 Premium (39.99€/mois)
- Accès aux cours base + intermédiaires
- Forum complet
- Webinaires mensuels
- Interprétation des rêves (IA avancée)
- Tirages de tarot multiples
- Horoscope natal

### 🥇 Gold (79.99€/mois)
- Accès à tous les cours
- Accès prioritaire aux nouveautés
- Sessions Q&A privées
- Réductions marketplace
- Tous les outils ésotériques
- Horoscope complet PDF
- Support expert

## 🛠️ Fonctionnalités Administrateur

### Gestion des Cours
- Ajout manuel de cours
- Génération de cours par IA (OpenAI)
- Import en masse (CSV/Excel)
- Gestion des modules et leçons

### Gestion des Utilisateurs
- Vue d'ensemble des membres
- Modification des niveaux d'abonnement
- Modération des contenus

### Analytics
- Statistiques d'utilisation
- Cours les plus populaires
- Revenus et abonnements

## 🔒 Sécurité

- **Chiffrement des mots de passe** avec bcrypt
- **Sessions sécurisées** avec Flask-Login
- **Protection CSRF** avec Flask-WTF
- **Validation des données** côté serveur
- **Sanitisation des entrées** utilisateur

## 🌐 Déploiement

### Variables d'Environnement Requises
```env
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///arcanum_eveille.db
OPENAI_API_KEY=your-openai-key
STRIPE_PUBLISHABLE_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
```

### Déploiement en Production
1. Utiliser un serveur WSGI (Gunicorn, uWSGI)
2. Configurer un reverse proxy (Nginx)
3. Utiliser une base de données robuste (PostgreSQL)
4. Configurer HTTPS avec certificats SSL
5. Mettre en place des sauvegardes automatiques

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Veuillez :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou support :
- **Email** : <EMAIL>
- **Documentation** : Consultez ce README
- **Issues** : Utilisez le système d'issues GitHub

---

*Développé avec passion pour les chercheurs de vérité et les explorateurs de l'invisible* 🌙✨

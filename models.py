from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    subscription_level = db.Column(db.String(20), default='standard')
    subscription_expires = db.Column(db.DateTime)
    birth_date = db.Column(db.Date)
    birth_time = db.Column(db.Time)
    birth_place = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_admin = db.Column(db.<PERSON><PERSON>, default=False)
    
    # Relations
    enrollments = db.relationship('Enrollment', backref='user', lazy=True)
    dream_interpretations = db.relationship('DreamInterpretation', backref='user', lazy=True)
    tarot_readings = db.relationship('TarotReading', backref='user', lazy=True)
    orders = db.relationship('Order', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_access_to_course(self, course):
        if self.subscription_level == 'gold':
            return True
        elif self.subscription_level == 'premium' and course.level in ['standard', 'premium']:
            return True
        elif self.subscription_level == 'standard' and course.level == 'standard':
            return True
        return False

class Course(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(100))
    level = db.Column(db.String(20), default='standard')  # standard, premium, gold
    instructor = db.Column(db.String(100))
    image_url = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_published = db.Column(db.Boolean, default=True)
    
    # Relations
    modules = db.relationship('Module', backref='course', lazy=True, cascade='all, delete-orphan')
    enrollments = db.relationship('Enrollment', backref='course', lazy=True)

class Module(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    order = db.Column(db.Integer, default=0)
    
    # Relations
    lessons = db.relationship('Lesson', backref='module', lazy=True, cascade='all, delete-orphan')

class Lesson(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    module_id = db.Column(db.Integer, db.ForeignKey('module.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)
    video_url = db.Column(db.String(255))
    order = db.Column(db.Integer, default=0)

class Enrollment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    enrolled_at = db.Column(db.DateTime, default=datetime.utcnow)
    progress = db.Column(db.Float, default=0.0)  # Percentage completed

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100))
    image_url = db.Column(db.String(255))
    stock = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    total_amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(50), default='pending')  # pending, paid, shipped, completed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    items = db.relationship('OrderItem', backref='order', lazy=True)

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    
    product = db.relationship('Product', backref='order_items')

class DreamInterpretation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    dream_description = db.Column(db.Text, nullable=False)
    emotions = db.Column(db.String(500))
    symbols = db.Column(db.String(500))
    interpretation = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class TarotReading(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reading_type = db.Column(db.String(50))  # single, three_card, celtic_cross
    question = db.Column(db.String(500))  # User's question
    cards_drawn = db.Column(db.Text)  # JSON string of card data
    interpretation = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class PalmReading(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    client_name = db.Column(db.String(100))  # Nom du client
    hand_type = db.Column(db.String(20))  # left, right, both
    image_filename = db.Column(db.String(255))  # Nom du fichier image
    question = db.Column(db.String(500))  # Question de l'utilisateur
    palm_analysis = db.Column(db.Text)  # Analyse détaillée des lignes
    interpretation = db.Column(db.Text)  # Interprétation complète
    spiritual_advice = db.Column(db.Text)  # Conseils spirituels supplémentaires
    confidence_score = db.Column(db.Float)  # Score de confiance de l'IA
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class RuneReading(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    client_name = db.Column(db.String(100))  # Nom du client
    reading_type = db.Column(db.String(50))  # single, three_rune, five_rune, nine_rune
    question = db.Column(db.String(500))  # Question de l'utilisateur
    runes_drawn = db.Column(db.Text)  # JSON des runes tirées
    rune_analysis = db.Column(db.Text)  # Analyse détaillée des runes
    interpretation = db.Column(db.Text)  # Interprétation spirituelle
    volva_guidance = db.Column(db.Text)  # Guidance de la Völva
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class ForumPost(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='forum_posts')
    replies = db.relationship('ForumReply', backref='post', lazy=True)

class ForumReply(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    post_id = db.Column(db.Integer, db.ForeignKey('forum_post.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='forum_replies')
